// ==UserScript==
// @name         品葱
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  品葱美化/屏蔽关键词
// <AUTHOR>
// @match        https://pincong.rocks/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
	"use strict";

	// 自定义样式定义
	const customStyles = `
.rbc,
a.aw-load-more-content.disabled,
.aw-load-more-content,
.aw-mod.aw-text-align-justify,
#category_note,
.aw-common-list .aw-question-content .contribute {
  display: none;
}
body {
    margin: 0;
    height: 100%;
    font-size: 14px;
    line-height: 160%;
    color: #202734;
    background: #E2E4E6;
}
a {
    color: #202734;
    text-decoration: none !important;
}
.aw-top-nav > nav > ul > li > a.active, .navbar-collapse, .aw-top-nav > nav > ul > li > a:hover, .aw-top-nav > nav > ul > li > a:focus, .category .list > li.active, .category .list > li:hover, .aw-top-menu-wrap {
    background-color: #000;
}
.aw-top-nav > nav > ul > li > a:hover {
    text-shadow: 0 1px 10px rgba(255, 255, 255, 0.8);
}
.aw-publish-btn .dropdown-toggle,
.btn-primary:active, .btn-primary:focus {
    background-color: #323341;
    border-radius: 8px;
}
.aw-publish-btn .dropdown-menu {
    border-radius: 0 0 8px 8px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}
.nav-tabs {
    border-bottom: none;
}
.aw-container-wrap {
    margin-top: 30px;
    background: #E2E4E6;
}
.aw-content-wrap {
    border: none;
    border-radius: 8px;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.15);
}
.aw-common-list > div + div {
    border-top: none;
}
.category .list > li > a {
    color: #202734;
}
.aw-common-list .aw-item {
    min-height: 90px;
}
.aw-common-list .aw-question-content > span, .aw-common-list .aw-question-content p span,
.aw-common-list .aw-item .aw-question-content .aw-user-name {
    color: #697480;
  font-size: 12px;
}
a:hover, a:focus {
    color: #499ef3;
}
.aw-common-list .aw-question-content h4 {
    font-size: 15px;
}
.aw-question-tags {
    display: inline-block;
    padding: 0 8px;
    height: 18px;
    line-height: 18px;
    background-color: #fff;
    font-size: 12px;
    color: #697480;
    border: none;
    font-size: 11px;
    border-radius: 4px;
}
.aw-question-tags:hover {
    background-color: #d8f8f1;
    outline: 1px solid #a8d6cc;
    color: #576d68;
}
.aw-feed-list .aw-item .mod-head .aw-user-img img, .aw-user-name img, .aw-topic-name img {
    width: 44px;
    height: 44px;
    border: 2px solid #000;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}
.aw-verified {
    font-size: 9px;
    border-radius: 4px;
}
body > div.aw-container-wrap > div:nth-child(2) > div > div > div.col-sm-12.col-md-3.aw-side-bar > div:nth-child(2) > div.mod-body > ul > li:nth-child(4) > a,
body > div.aw-container-wrap > div:nth-child(2) > div > div > div.col-sm-12.col-md-3.aw-side-bar > div:nth-child(2) > div.mod-body > ul > li:nth-child(3) > a {
    color: #202734 !important;
}
.markitup-box {
    position: relative;
    z-index: 1;
    line-height: 1.8;
    font-size: 15px;
}
mark {
    text-decoration-line: none;
    color: #155faa;
}
.aw-side-bar .aw-mod {
    padding: 20px 0;
    border-top: none;
}
.markitup-box img {
    border-radius: 10px;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}
sup {
    top: -.5em;
    color: #999;
}
.topic-tag .text {
    display: inline-block;
    height: 20px;
    line-height: 18px;
    padding: 0 10px;
    background-color: #f5f5f5;
    font-size: 11px;
    color: #999;
    border-radius: 20px;
    border: 1px solid #e6e6e6 !important;
}
.btn-success:active,
.btn-success:focus {
    background-color: #32a831 !important;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}
.btn-success,
.btn-gray.active {
    border: none !important;
    background-color: #5bbf5a;
    color: #fff;
    border-radius: 8px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
}
.form-control {
    background-color: #e6e6e6;
    border: none;
    border-radius: 8px;
    border: 1px solid #e6e6e6;
    background: #fff;
}
.form-control:focus {
    box-shadow: none;
    background: #fff;
    border-color: #e6e6e6 !important;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.04);
}
.aw-comment-list .aw-item, .aw-comment-list .aw-item:last-child {
    border-bottom: none;
}
.aw-comment-box .aw-comment-list a {
    font-size: 13px;
    color: #202734;
}
.aw-feed-list .aw-item {
    border-bottom: none;
}
.aw-topic-bar .tag-bar .operate .btn-group .btn {
    border-radius: 8px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
    background: #499ef3;
    color: #fff;
}
.ab-video {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}
.btn-normal {
    min-width: 50px;
    height: 25px;
    padding: 0 10px;
    line-height: 25px;
    font-size: 12px;
    border-radius: 8px !important;
}
.aw-topic-bar .tag-bar .operate .btn-group .btn {
    width: 40px;
    height: 25px;
    line-height: 14px;
    padding: 0;
    font-size: 20px;
    border-radius: 8px !important;
}
.btn-mini {
    min-width: 50px;
}
.aw-comment-box {
    background-color: #f5f5f5;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
}
.aw-comment-box form {
    padding: 15px;
    background-color: #f5f5f5;
    border-top: none;
    border-radius: 0 0 8px 8px;
}
.aw-article-vote a.agree {
    border-radius: 8px 0 0 8px;
}
.aw-article-vote a.disagree {
    border-radius: 0 8px 8px 0;
}
.aw-dropdown {
    border: none;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}
.aw-dropdown .aw-dropdown-list li {
    border-top: none;
    text-align: center;
}
.aw-comment-list .aw-item .aw-user-img img {
    border-radius: 6px;
    border: 2px solid #000;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
}
.aw-side-bar .user-detail .aw-user-img img {
    width: 32px;
    height: 32px;
    border: 2px solid #000;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}
.aw-article-vote a {
    display: inline-block;
    float: left;
    height: 28px;
    line-height: 26px;
    padding: 0 13px 0 13px;
    color: #999;
    border: 1px solid #e6e6e6 !important;
    background-color: #eee;
}
.aw-article-vote a.disagree {
    border-radius: 8px;
    margin-left: 10px;
    border-left: none;
}
.aw-article-vote a.agree {
    border-radius: 8px;
}
.aw-article-vote b {
    margin-left: 0px;
    font-size: 12px;
}
.aw-question-detail .meta {
    margin-top: 10px;
}
.topic-tag .text:hover, .topic-tag .text:active, .topic-tag .close:hover, .topic-tag .close:active {
    background-color: #202734;
    color: #fff;
    border: 1px solid #202734 !important;
}
.aw-question-detail > .mod-head {
    border-bottom: none;
}
.tag-bar.clearfix span {
    margin-top: 2px;
}
.icon-inverse i {
    color: #666;
    font-size: 10px !important;
}
.icon-inverse {
    background-color: transparent;
}
.aw-feed-list .operate {
    background-color: transparent;
    border-radius: 0;
}
.aw-feed-list .operate a + a {
    border-left: none;
}
.aw-feed-list .operate a.disagree, .aw-feed-list .operate a.agree {
    border-radius: 6px;
    background: #eee;
    color: #999;
    height: 25px;
    line-height: 25px;
    border: 1px solid #e6e6e6;
}
.aw-feed-list .operate a.disagree {
    margin-left: 7px;
    width: 35px;
    text-align: center;
}
.aw-feed-list .meta i, .aw-question-detail .meta i {
    font-size: 13px;
    vertical-align: 0;
    color: #888;
}
.aw-nav-tabs > li.active a {
    line-height: 30px;
    background-color: #5abf5a !important;
    border: none;
    border-bottom: none;
    font-weight: bold;
    color: #fff !important;
    border-radius: 8px;
}
.aw-nav-tabs > li.active a:hover, .aw-nav-tabs > li.active a:focus, .aw-nav-tabs > li > a:focus {
    border: none;
    border-bottom: none;
    background-color: #fff;
    color: #fff;
}
.aw-nav-tabs > li > a {
    color: #202734;
}
.aw-nav-tabs > li > a:hover {
    border: none;
    border-bottom: none;
    background-color: #fff;
    color: #202734;
}
.aw-feed-list .operate a:hover i, .aw-feed-list .operate a.active i {
    color: #fff;
}
.aw-feed-list .operate a:hover {
    border: 1px solid #202734;
    color: #fff;
    background-color: #202734;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}
.icon.icon-disagree:hover {
    color: #fff;
}
.aw-feed-list .operate a:hover, .aw-feed-list .operate a.active {
    background-color: #202734;
    color: #fff;
    border: 1px solid #202734;
}
.aw-small-text {
    font-size: 12px;
    color: #999;
    padding: 0 7px;
}
.aw-article-reply-box .mod-footer input[type="text"] {
    width: 52px;
    height: 25px;
    font-size: 12px;
}
.aw-article-reply-box .mod-footer input[type="text"] {
    width: 55px;
    height: 25px;
    font-size: 12px;
    margin-top: 4px;
}
.aw-feed-list .operate a.agree {
    width: 50px;
    text-align: center;
}
.aw-feed-list .operate a {
    padding: 0;

}
.aw-dropdown .aw-dropdown-list li {
    border-radius: 8px;
    margin: 5px !important;
}
.aw-dropdown .aw-dropdown-list li a {
    font-size: 13px;
}
.aw-topic-bar .tag-bar .operate .aw-dropdown {
    min-width: 90px;
}
.aw-dropdown .aw-dropdown-list li:hover, .aw-dropdown .aw-dropdown-list li.active {
    background-color: #eee;
    margin: 5px !important;
}
.aw-article-vote a:hover, .aw-article-vote a.active {
    background-color: #202734;
    color: #fff;
    border: 1px solid #202734;
   box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}
.markitup-box blockquote {
    font-size: 14px;
    color: #333;
    border-left: none;
    background: #f5f5f5;
    border-radius: 8px;
  margin: 0 0 5px 0;
}
.markitup-box > blockquote > a {
    color: #777;
}
.alert-warning {
    background: #fcfddb;
    border: none;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}
.aw-topic-bar .tag-bar .operate .btn-group.open .btn {
    background-color: #202734;
    color: #fff;
}
.aw-add-comment:hover i,
.aw-add-comment:hover {
    background: #fff !important;
    border: none !important;
    box-shadow: none !important;
  color:#777!important;
}
.aw-feed-list .operate + .operate {
    margin-left: 15px;
}
.aw-text-color-666, .alert, .aw-nav-tabs > li.active a, .aw-comment-list, .aw-question-edit .aw-mod-body, .aw-question-tags a:hover, .aw-edit-topic:hover, .aw-tabs ul li a, .aw-footer {
    color: #202734;
}
.aw-feed-list .operate .aw-add-comment:hover, .aw-feed-list .operate .aw-add-comment.active {
    background-color: transparent;
    color: green;
    border-radius: 4px;
    border: none;
}
.aw-feed-list .operate .aw-add-comment:hover i,
.aw-feed-list .operate .aw-add-comment.active i {
    background-color: transparent;
    color: green;
    border-radius: 4px;
    border: none;
}
.aw-question-detail .mod-body {
    font-size: 15px;
    color: #202734;
}
.aw-question-detail .mod-head h1 {
    color: #202734;
    font-size: 19px;
    font-weight: bold !important;
}
.aw-item > .markitup-box {
    font-size: 14px;
}
.markitup-box pre {
    margin: 10px 0;
    padding: 16px;
    background-color: #ffefce;
    font-size: 14px;
    border-radius: 10px;
}
.mod-head {
    color: green;
    font-size: 13px;
}
.aw-feed-list .aw-item .mod-head .aw-user-name {
    color: #000;
}
.aw-feed-list .aw-item .mod-head, .aw-feed-list .aw-item .mod-body, .aw-feed-list .aw-item .mod-footer {
    padding-left: 54px;
}
.aw-small-text.hidden-xxs {
    margin-top: 6px;
}
.aw-common-list .aw-question-content h4 {
    padding: 0 6px;
}
/* 针对可能的内联编辑器 */
[contenteditable="true"] {
    background-color: #fff !important;
}

/* 屏蔽关键词 */
.trigger-area {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    z-index: 999;
}

.block-list-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    padding: 5px 10px;
    background: #202734;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.trigger-area:hover .block-list-button {
    opacity: 1;
    pointer-events: auto;
}
.block-title {
    font-size: 14px;
    margin: 0 0 5px 0;
    color: #202734;
}
.block-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 500px;
    max-width: 90vw;
    height: auto;
    max-height: 80vh;
    background: rgba(255, 255, 255, 0.7);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 1px 20px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    overflow: auto;
}
.block-dialog::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    z-index: -1;
    backdrop-filter: blur(50px);
}
.block-title-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: none;
    cursor: move;
    user-select: none;
}
.block-close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 16px;
    height: 16px;
    background: black;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.15s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    text-indent: -9999px;
    padding: 0;
    z-index: 1002;
}
.block-close-button:hover {
    background: red;
}
.block-close-line {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 2px;
    background: white;
    transition: background 0.15s ease;
}
.block-close-line-1 {
    transform: translate(-50%, -50%) rotate(45deg);
}
.block-close-line-2 {
    transform: translate(-50%, -50%) rotate(-45deg);
}
.block-list {
    margin: 20px 0 0 0;
    max-height: 220px;
    font-size: 12px;
    padding: 10px;
    background: rgba(32, 39, 52, 0.05);
    border-radius: 8px;
}
.block-keyword-list {
    margin: 15px 0 0 0;
    max-height: 100px;
    color: #666;
}
.block-input-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin: 15px 0;
}
.block-input {
    border: 1px solid #dddddd;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
    font-size: 13px;
    padding: 5px 10px;
    width: 180px;
}
.block-input:focus {
    border: 1px solid #999;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1)
}
.block-button {
    border-radius: 8px;
    border: 0px solid black;
    background: #202734;
    padding: 5px 10px;
    color: white;
    font-size: 13px;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}
    `;

	const STYLE_ID = "pincong-enhanced-styles";

	function injectStyles() {
		// 避免重复注入
		if (document.getElementById(STYLE_ID)) return;

		// 优先使用 GM_addStyle
		if (typeof GM_addStyle === "function") {
			try {
				GM_addStyle(customStyles);
				return;
			} catch {} // 静默失败，转用备选方案
		}

		// 备选方案：创建 style 元素
		const styleEl = document.createElement("style");
		styleEl.id = STYLE_ID;
		styleEl.textContent = customStyles;

		// 优化注入时机
		if (document.head) {
			requestAnimationFrame(() => document.head.appendChild(styleEl));
		} else {
			// 仅在必要时使用 MutationObserver
			const observer = new MutationObserver((mutations, obs) => {
				if (document.head) {
					requestAnimationFrame(() => document.head.appendChild(styleEl));
					obs.disconnect();
				}
			});
			observer.observe(document.documentElement, { childList: true });
		}
	}

	// 在 DOMContentLoaded 之前注入
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", injectStyles, { once: true });
	} else {
		injectStyles();
	}
})();
