// ==UserScript==
// @name         贴吧内容屏蔽
// @namespace    http://tampermonkey.net/
// @version      1.3.6
// @description  屏蔽贴吧用户和关键词
// <AUTHOR> name
// @match        *://tieba.baidu.com/*
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function () {
	"use strict";

	// 添加样式
	const style = document.createElement("style");
	style.textContent = `
.block-list-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    padding: 5px 10px;
    background: #000;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.block-dialog {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 600px;
	max-width: 90vw;
	height: auto;
	max-height: 80vh;
	padding: 20px;
	border-radius: 10px;
	z-index: 1001;
	overflow: auto;
	background: rgba(250, 250, 252, 0.8);
	border: 1px solid rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(30px) saturate(180%);
	box-shadow: -5px 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 30px rgba(255, 255, 255, 0.9);
}

.block-title-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: none;
    cursor: move;
    user-select: none;
}

.block-close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 16px;
    height: 16px;
    background: black;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.15s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    text-indent: -9999px;
    padding: 0;
    z-index: 1002;
}

.block-close-button:hover {
    background: red;
}

.block-close-line {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 2px;
    background: white;
    transition: background 0.15s ease;
}

.block-close-line-1 {
    transform: translate(-50%, -50%) rotate(45deg);
}

.block-close-line-2 {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.block-list {
	margin: 20px 0 0 0;
	max-height: 220px;
	font-size: 12px;
	padding: 10px;
	border-radius: 8px;
	background: rgb(131, 131, 145, 0.15);
}

.block-keyword-list {
    margin: 15px 0 0 0;
    max-height: 100px;
}

.block-input-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    width: 100%;
}

.block-input-group {
    display: flex;
    align-items: center;
    width: 45%;
}

.keyword-input-group {
    display: flex;
    align-items: center;
    width: 50%;
    margin: 0 auto;
}

.block-input {
	flex: 1;
	border: none;
	border-radius: 8px;
	font-size: 13px;
	padding: 5px 10px;
	margin-right: 10px;
	min-width: 0;
	background: rgb(131, 131, 145, 0.15);
}

.block-input:focus {
	outline: 1px solid rgba(255, 255, 255, 0.2);
	background: rgb(131, 131, 145, 0.2);
}

.block-button {
    border-radius: 8px;
    border: 0px solid black;
    background: #000;
    padding: 5px 10px;
    color: white;
    font-size: 13px;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    min-width: fit-content;
}

.trigger-area {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    z-index: 999;
}

.trigger-area:hover .block-list-button {
    opacity: 1;
    pointer-events: auto;
}
    `;
	document.head.appendChild(style);

	// 存储类定义
	class I {
		constructor(name, defaultValue, storage) {
			this.name = name;
			this.defaultValue = defaultValue;
			this.storage = storage;
		}

		get() {
			return this.storage.getValue(this.name, this.defaultValue);
		}

		set(value) {
			this.storage.setValue(this.name, value);
		}

		remove() {
			this.set([]);
		}
	}

	// 初始化存储
	const mt = new I("shieldList", [], {
		getValue: GM_getValue,
		setValue: GM_setValue,
	});

	// 获取存储的数据并处理数据结构
	let storedData = mt.get() || [];
	let blockedUsers = storedData
		.filter((item) => item.type === "user")
		.map((item) => {
			if (typeof item.content === "object" && item.content !== null) {
				return item.content.content || item.content.toString();
			}
			return item.content.toString();
		});
	let blockedKeywords = storedData
		.filter((item) => item.type === "keyword")
		.map((item) => {
			if (typeof item.content === "object" && item.content !== null) {
				return item.content.content || item.content.toString();
			}
			return item.content.toString();
		});

	// 保存数据
	function saveBlockedLists() {
		const combinedList = [
			...blockedUsers.map((content) => ({
				content: content.toString(),
				type: "user",
			})),
			...blockedKeywords.map((content) => ({
				content: content.toString(),
				type: "keyword",
			})),
		];
		mt.set(combinedList);
	}

	// 添加屏蔽按钮
	function addBlockListButton() {
		const button = document.createElement("button");
		button.innerText = "屏蔽列表";
		button.className = "block-list-button";
		button.addEventListener("click", showBlockListDialog);
		return button;
	}
	// 创建触发区域
	const triggerArea = document.createElement("div");
	triggerArea.className = "trigger-area";
	triggerArea.appendChild(addBlockListButton());
	document.body.appendChild(triggerArea);
	// 显示屏蔽列表对话框
	function showBlockListDialog() {
		const dialog = document.createElement("div");
		dialog.className = "block-dialog";

		// 创建标题栏作为拖动区域
		const titleBar = document.createElement("div");
		titleBar.className = "block-title-bar";

		// 拖动实现
		let isDragging = false;
		let startX, startY;
		let initialLeft, initialTop;

		function startDragging(e) {
			if (e.target === titleBar) {
				isDragging = true;
				startX = e.clientX;
				startY = e.clientY;

				const rect = dialog.getBoundingClientRect();
				initialLeft = rect.left;
				initialTop = rect.top;

				dialog.style.transform = "none";
				dialog.style.left = `${initialLeft}px`;
				dialog.style.top = `${initialTop}px`;
			}
		}

		function doDrag(e) {
			if (!isDragging) return;

			e.preventDefault();
			const deltaX = e.clientX - startX;
			const deltaY = e.clientY - startY;

			dialog.style.left = `${initialLeft + deltaX}px`;
			dialog.style.top = `${initialTop + deltaY}px`;
		}

		function stopDragging() {
			isDragging = false;
		}

		titleBar.addEventListener("mousedown", startDragging);
		document.addEventListener("mousemove", doDrag);
		document.addEventListener("mouseup", stopDragging);

		// 创建关闭按钮
		const closeButton = document.createElement("button");
		closeButton.className = "block-close-button";

		// 创建关闭按钮的X图标
		const before = document.createElement("span");
		before.className = "block-close-line block-close-line-1";

		const after = document.createElement("span");
		after.className = "block-close-line block-close-line-2";

		closeButton.appendChild(before);
		closeButton.appendChild(after);

		closeButton.onclick = () => document.body.removeChild(dialog);

		// 显示当前屏蔽的用户名（最新50个）
		const userList = document.createElement("div");
		userList.className = "block-list";
		const recentUsers = blockedUsers.slice(-50).reverse();
		userList.innerHTML =
			`<h3>屏蔽的用户名 (最新${recentUsers.length}个):</h3>` +
			recentUsers.join(", ") +
			(blockedUsers.length > 50
				? `<div style="color: #666; margin-top: 5px;">...等共${blockedUsers.length}个用户</div>`
				: "");

		// 创建用户名输入区域容器
		const userInputContainer = document.createElement("div");
		userInputContainer.className = "block-input-container";

		// 创建用户名输入组
		const userInputGroup = document.createElement("div");
		userInputGroup.className = "block-input-group";

		// 创建username输入组
		const usernameInputGroup = document.createElement("div");
		usernameInputGroup.className = "block-input-group";

		// 创建添加用户名的输入框和按钮
		const addUserInput = document.createElement("input");
		addUserInput.type = "text";
		addUserInput.placeholder = "输入用户名";
		addUserInput.className = "block-input";

		// 添加获取焦点和失去焦点事件
		addUserInput.addEventListener('focus', function() {
			this.placeholder = '';
		});
		addUserInput.addEventListener('blur', function() {
			if (!this.value) {
				this.placeholder = '输入用户名';
			}
		});

		const addUserButton = document.createElement("button");
		addUserButton.innerText = "添加";
		addUserButton.className = "block-button";

		// 创建用户名输入框（通过username属性）
		const addUsernameInput = document.createElement("input");
		addUsernameInput.type = "text";
		addUsernameInput.placeholder = "输入username";
		addUsernameInput.className = "block-input";

		// 添加获取焦点和失去焦点事件
		addUsernameInput.addEventListener('focus', function() {
			this.placeholder = '';
		});
		addUsernameInput.addEventListener('blur', function() {
			if (!this.value) {
				this.placeholder = '输入username';
			}
		});

		const addUsernameButton = document.createElement("button");
		addUsernameButton.innerText = "添加";
		addUsernameButton.className = "block-button";

		// 添加用户名事件处理
		addUserButton.onclick = () => {
			const newUserID = addUserInput.value.trim();
			if (newUserID && !blockedUsers.includes(newUserID)) {
				blockedUsers.push(newUserID);
				updateUserList();
				addUserInput.value = "";
				saveBlockedLists();
				hideBlockedContent();
			}
		};

		// 添加username事件处理
		addUsernameButton.onclick = () => {
			const newUsername = addUsernameInput.value.trim();
			if (newUsername && !blockedUsers.includes(newUsername)) {
				blockedUsers.push(newUsername);
				updateUserList();
				addUsernameInput.value = "";
				saveBlockedLists();
				hideBlockedContent();
			}
		};

		// 更新用户列表显示的函数
		function updateUserList() {
			const recentUsers = blockedUsers.slice(-50).reverse();
			userList.innerHTML =
				`<h3>屏蔽的用户 (最新${recentUsers.length}个):</h3>` +
				recentUsers.join(", ") +
				(blockedUsers.length > 50
					? `<div style="color: #666; margin-top: 5px;">...等共${blockedUsers.length}个用户</div>`
					: "");
		}

		// 显示当前屏蔽的关键词（最新50个）
		const keywordList = document.createElement("div");
		keywordList.className = "block-list block-keyword-list";
		const recentKeywords = blockedKeywords.slice(-50).reverse();
		keywordList.innerHTML =
			`<h3>屏蔽的关键词 (最新${recentKeywords.length}个):</h3>` +
			recentKeywords.join(", ") +
			(blockedKeywords.length > 50
				? `<div style="color: #666; margin-top: 5px;">...等共${blockedKeywords.length}个关键词</div>`
				: "");

		// 创建关键词输入区域容器
		const keywordInputContainer = document.createElement("div");
		keywordInputContainer.className = "block-input-container";
		keywordInputContainer.style.justifyContent = "center";

		// 创建关键词输入组
		const keywordInputGroup = document.createElement("div");
		keywordInputGroup.className = "keyword-input-group";

		// 创建添加关键词的输入框和按钮
		const addKeywordInput = document.createElement("input");
		addKeywordInput.type = "text";
		addKeywordInput.placeholder = "输入关键词";
		addKeywordInput.className = "block-input";

		// 添加获取焦点和失去焦点事件
		addKeywordInput.addEventListener('focus', function() {
			this.placeholder = '';
		});
		addKeywordInput.addEventListener('blur', function() {
			if (!this.value) {
				this.placeholder = '输入关键词';
			}
		});

		const addKeywordButton = document.createElement("button");
		addKeywordButton.innerText = "添加关键词";
		addKeywordButton.className = "block-button";

		// 添加关键词事件处理
		addKeywordButton.onclick = () => {
			const newKeyword = addKeywordInput.value.trim();
			if (newKeyword && !blockedKeywords.includes(newKeyword)) {
				blockedKeywords.push(newKeyword);
				updateKeywordList();
				addKeywordInput.value = "";
				saveBlockedLists();
				hideBlockedContent();
			}
		};

		// 更新关键词列表显示的函数
		function updateKeywordList() {
			const recentKeywords = blockedKeywords.slice(-50).reverse();
			keywordList.innerHTML =
				`<h3>屏蔽的关键词 (最新${recentKeywords.length}个):</h3>` +
				recentKeywords.join(", ") +
				(blockedKeywords.length > 50
					? `<div style="color: #666; margin-top: 5px;">...等共${blockedKeywords.length}个关键词</div>`
					: "");
		}

		// 将元素添加到对话框
		dialog.appendChild(titleBar);
		dialog.appendChild(closeButton);
		dialog.appendChild(userList);

		// 添加用户和username输入组到容器
		userInputGroup.appendChild(addUserInput);
		userInputGroup.appendChild(addUserButton);
		usernameInputGroup.appendChild(addUsernameInput);
		usernameInputGroup.appendChild(addUsernameButton);

		userInputContainer.appendChild(userInputGroup);
		userInputContainer.appendChild(usernameInputGroup);
		dialog.appendChild(userInputContainer);

		dialog.appendChild(keywordList);

		// 添加关键词输入到容器
		keywordInputGroup.appendChild(addKeywordInput);
		keywordInputGroup.appendChild(addKeywordButton);
		keywordInputContainer.appendChild(keywordInputGroup);
		dialog.appendChild(keywordInputContainer);

		document.body.appendChild(dialog);
	}

	// 隐藏被屏蔽的内容
	function hideBlockedContent() {
		const selectors = {
			thread: {
				container: ".j_thread_list",
				username: ".tb_icon_author, .frs-author-name, img[username]",
				content: ".threadlist_title a, .threadlist_abs",
			},
			post: {
				container: ".l_post_bright",
				username: ".d_name a, .p_author_name, img[username]",
				content: ".d_post_content",
			},
			comment: {
				container: ".lzl_single_post",
				username: ".lzl_cnt .j_user_card, img[username]",
				content: ".lzl_content",
			},
		};

		// 获取完整的用户名(username)
		function getFullUsername(element) {
			// 检查是否有title属性包含"主题作者:"
			const titleElement = element.querySelector(
				'.tb_icon_author[title^="主题作者:"]'
			);
			if (titleElement) {
				const titleText = titleElement.getAttribute("title");
				// 从title中提取完整用户名
				const match = titleText.match(/主题作者:\s*(.*)/);
				if (match && match[1]) {
					return match[1].trim();
				}
			}

			// 检查是否有data-field属性包含un(username)
			const userCardElement = element.querySelector(".j_user_card[data-field]");
			if (userCardElement) {
				try {
					const dataField = JSON.parse(
						userCardElement.getAttribute("data-field")
					);
					if (dataField && dataField.un) {
						return dataField.un;
					}
				} catch (e) {
					// 解析JSON失败时继续检查其他方法
				}
			}

			return null;
		}

		// 在 hideBlockedContent 函数中修改关键词检查部分
		Object.values(selectors).forEach(({ container, username, content }) => {
			document.querySelectorAll(container).forEach((elem) => {
				let shouldHide = false;

				// 首先尝试获取完整的username
				const fullUsername = getFullUsername(elem);

				// 如果找到完整username并且在屏蔽列表中，则隐藏
				if (
					fullUsername &&
					blockedUsers.some((user) => fullUsername.includes(user))
				) {
					shouldHide = true;
				} else {
					// 检查用户名（包括 username 属性）
					const authorElems = elem.querySelectorAll(username);
					if (authorElems) {
						authorElems.forEach((authorElem) => {
							const authorName =
								authorElem.getAttribute("username") ||
								authorElem.textContent.trim();
							if (blockedUsers.some((user) => authorName.includes(user))) {
								shouldHide = true;
							}
						});
					}
				}

				// 检查内容关键词（修改为不区分大小写）
				if (!shouldHide) {
					const contentElem = elem.querySelector(content);
					if (contentElem) {
						const contentText = contentElem.textContent.trim().toLowerCase(); // 转换为小写
						if (
							blockedKeywords.some((keyword) =>
								contentText.includes(keyword.toLowerCase())
							)
						) {
							// 关键词也转换为小写
							shouldHide = true;
						}
					}
				}

				if (shouldHide) {
					elem.style.display = "none";
				}
			});
		});
	}

	// 初始化
	function init() {
		addBlockListButton();
		hideBlockedContent();

		// 监听页面变化
		const observer = new MutationObserver(hideBlockedContent);
		observer.observe(document.body, {
			childList: true,
			subtree: true,
		});
	}

	// 页面加载完成后初始化
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", init);
	} else {
		init();
	}
})();
