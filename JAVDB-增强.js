// ==UserScript==
// @name         JAVDB-增强
// @version      1.0
// @license      MIT
// @icon         https://www.google.com/s2/favicons?sz=64&domain=javdb.com
// @match        https://javdb.com/*
// @match        https://subtitlecat.com/*
// @require      data:application/javascript,;(function%20hookBody()%20%7B%20const%20initialHideStyle%20%3D%20document.createElement(%22style%22)%3B%20initialHideStyle.textContent%20%3D%20%60%20body%20%7B%20opacity%3A%200%20!important%3B%20visibility%3A%20hidden%20!important%3B%20%7D%20body.script-ready%20%7B%20opacity%3A%201%20!important%3B%20visibility%3A%20visible%20!important%3B%20%7D%20%60%3B%20document.head.appendChild(initialHideStyle)%3B%20%7D)()%3B
// @require      https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js
// @require      https://cdn.jsdelivr.net/npm/layui@2.11.0/dist/layui.min.js
// @require      https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js
// @connect      xunlei.com
// @connect      ja.wikipedia.org
// @connect      *
// @grant        GM_addStyle
// @grant        GM_xmlhttpRequest
// @run-at       document-start
// ==/UserScript==

// 集中管理样式
const STYLES = {
    // 页面加载时的初始化样式
    pageInitial: `

body {
    opacity: 0 !important;
    visibility: hidden !important;
}
body.script-ready {
    opacity: 1 !important;
    visibility: visible !important;
}
.t-banner-inner {
display: none !important;
}
`,

// 按钮通用样式
menuBtn: `
.menu-btn {
    display: inline-block !important;
    min-width: 80px;
    padding: 7px 12px;
    border-radius: 4px;
    color: #fff !important;
    text-decoration: none;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    transition: all .3s ease;
    box-shadow: 0 2px 5px #0000001a;
    border: none;
    line-height: 1.3;
    margin: 0
}
`,

// 演员信息弹出层样式
actressPopup: `
.actress-popup {
    position: absolute;
    z-index: 10000;
    background: rgba(240, 240, 240, 0.7);
    backdrop-filter: blur(30px) saturate(180%) !important;
    border-radius: 12px;
    padding: 20px;
    width: 250px;
    border: 1px solid rgba(255, 255, 255, 0.6) !important;
    box-shadow: 0 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 30px 5px rgba(255, 255, 255, 0.5)  !important;
    display: none;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    pointer-events: none;
}

.actress-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50% !important;
    object-fit: cover;
    margin-right: 10px;
    border: 3px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

.actress-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.actress-name {
    font-size: 18px;
    text-shadow: 0 0 30px rgba(255, 0, 0, 0.15) !important;
    font-weight: bold;
    color: #E15B6E;
    margin: 0;
    flex: 1;
}

.actress-info-row {
    display: flex;
    margin-bottom: 8px;
}

.actress-info-label {
    width: 60px;
    font-weight: bold;
    color: #333;
}

.actress-info-value {
    flex: 1;
}


`,

// 字幕相关样式
subtitleStyles: `
#search-subtitle-btn, #xunLeiSubtitleBtn {
    background: rgba(156, 100, 219, 0.6);
}

#search-subtitle-btn {
    margin-right: 10px;
}

#table-container {
    padding: 15px;
}

.xunlei-no-subtitle {
    text-align: center;
    color: #999;
    padding: 20px;
}

.xunlei-error {
    color: red;
    margin-top: 10px;
}

.actress-loading {
    text-align: center;
}

.actress-error {
    text-align: center;
    color: #e74c3c;
}

.layui-layer {
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%, -50%) !important;
	width: 680px !important;
	max-height: 70vh !important;
	overflow-y: auto !important;
	background-color: rgba(240, 242, 244, 0.85) !important;
	border: 1px solid rgba(255, 255, 255, 0.4);
	box-shadow: 0px 1px 50px rgba(0, 0, 0, 0.20), inset 0 0 90px 30px rgba(255, 255, 255, 0.9) !important;
	backdrop-filter: blur(50px) !important;
}

.layui-table {
	width: 100%;
	margin: 10px 0;
	background-color: transparent !important;
	color: #5f5f5f;
}

.layui-layer-title {
padding: 0 !important;
	border-bottom: none !important;
    text-align: center !important;
    font-size: 18px !important;
    font-weight: bold !important;
}

.layui-table td, .layui-table th, .layui-table-col-set, .layui-table-fixed-r, .layui-table-grid-down, .layui-table-header, .layui-table-mend, .layui-table-page, .layui-table-tips-main, .layui-table-tool, .layui-table-total, .layui-table-view, .layui-table[lay-skin="line"], .layui-table[lay-skin="row"] {
	border-width: 0px !important;
}

table {
  border-collapse: separate;
  border-spacing: 0;
}

.layui-layer-shade {
	display: none;
}

.layui-table tr {
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  margin: 15px;
  font-weight: 500;
  color: #333;
}

.layui-table td, .layui-table th {
  padding: 5px 15px !important;
  line-height: 48px !important;
}

.layui-btn {
	background-color: #f4b16b !important;
}
`
};

// 样式注入器
(function injectStyles() {
    // 注入所有样式
    if (typeof GM_addStyle === "function") {
        try {
            // 使用GM_addStyle注入所有样式
            Object.values(STYLES).forEach(css => GM_addStyle(css));
        } catch (error) {
            console.error('GM_addStyle失败，使用备用方式注入样式', error);
            // 备用样式注入方式
            Object.values(STYLES).forEach(css => {
                const style = document.createElement('style');
                style.textContent = css;
                document.head.appendChild(style);
            });
        }
    } else {
        // 备用样式注入方式
        Object.values(STYLES).forEach(css => {
            const style = document.createElement('style');
            style.textContent = css;
            document.head.appendChild(style);
        });
    }
})();

(function ($, layui) {
    'use strict';

    // 基础工具类
    class Utils {
        constructor() {
            this.initialized = false;
        }

        importResource(url) {
            const resourceType = url.endsWith('.css') ? 'style' : 'script';
            if (resourceType === 'style') {
                $('head').append(`<link rel="stylesheet" href="${url}">`);
            } else {
                $('head').append(`<script src="${url}"></script>`);
            }
        }

        insertStyle(css) {
            if (!css) return;
            const style = document.createElement('style');
            style.textContent = css;
            document.head.appendChild(style);
        }

        openPage(url, title, close, e) {
            if (e && e.ctrlKey) {
                window.open(url, "_blank");
                return;
            }
            window.open(url, "_blank");
        }

        q(event, text, callback, cancelCallback) {
            if (window.confirm(text)) {
                callback && callback();
            } else {
                cancelCallback && cancelCallback();
            }
        }
    }

    // 插件基类
    class BasePlugin {
        constructor() {
            this.pluginManager = null;
        }

        injectBean() {}

        async initCss() {
            return "";
        }

        async handle() {}

        log(...args) {
            console.log(`[${this.constructor.name}]`, ...args);
        }
    }

    // 插件管理器
    class PluginManager {
        constructor(debugPlugins) {
            this.plugins = new Map();
            this.isInitialized = false;
            this.canLogPlugins = new Set(Array.isArray(debugPlugins) ? debugPlugins : debugPlugins.split(','));
            this.logAll = this.canLogPlugins.has('*');
        }

        register(pluginClass) {
            if (typeof pluginClass !== 'function') {
                throw new Error('插件必须是一个类');
            }

            const className = pluginClass.name;
            if (!className) {
                throw new Error('类必须要有名称');
            }

            const pluginName = className.toLowerCase();
            if (this.plugins.has(pluginName)) {
                throw new Error(`插件"${className}"已注册`);
            }

            const instance = new pluginClass();
            instance.pluginManager = this;

            if (!this.logAll && !this.canLogPlugins.has(pluginName)) {
                instance.log = function() {};
            }

            this.plugins.set(pluginName, instance);
        }

        getBean(name) {
            return this.plugins.get(name.toLowerCase());
        }

        _initialize() {
            if (this.isInitialized) return;

            const pluginsWithDeps = new Map();

            for (const [name, instance] of this.plugins) {
                if (typeof instance.injectBean === 'function') {
                    pluginsWithDeps.set(name, {
                        instance,
                        deps: this._getDependencies(instance.injectBean)
                    });
                }
            }

            for (const [name, { instance, deps }] of pluginsWithDeps) {
                const depInstances = deps.map(depName => {
                    const lowerDepName = depName.toLowerCase();
                    if (!this.plugins.has(lowerDepName)) {
                        throw new Error(`插件"${name}"依赖的插件"${depName}"未注册`);
                    }
                    return this.plugins.get(lowerDepName);
                });

                instance.injectBean(...depInstances);
            }

            this.isInitialized = true;
        }

        _getDependencies(injectBeanFn) {
            const fnStr = injectBeanFn.toString();
            return fnStr
                .slice(fnStr.indexOf('(') + 1, fnStr.indexOf(')'))
                .split(',')
                .map(param => param.trim())
                .filter(param => param);
        }

        async process() {
            if (!this.isInitialized) {
                this._initialize();
            }

            const failures = (await Promise.allSettled(
                Array.from(this.plugins).map(async ([name, instance]) => {
                    try {
                        if (typeof instance.handle === 'function') {
                            await instance.handle();
                            return { name, status: 'fulfilled' };
                        }
                    } catch (error) {
                        console.error(`插件 ${name} 执行失败`, error);
                        return { name, status: 'rejected', error };
                    }
                })
            )).filter(result => result.status === 'rejected');

            if (failures.length) {
                console.error('以下插件执行失败：', failures.map(f => f.name));
            }

            $('body').addClass('script-ready');
        }
    }

    // JavDb相关基类
    class JavDbPlugin extends BasePlugin {
        constructor() {
            super();
            let url = window.location.href.split('?')[0];
            this.isDetailPage = url.includes('javdb') && url.includes('/v/');
        }

        getPageInfo() {
            return {
                carNum: $('a[title="複製番號"]').attr('data-clipboard-text'),
                url: window.location.href.split('#')[0],
                actress: $('.female').prev().map((i, el) => $(el).text()).get().join(' '),
                actors: $('.male').prev().map((i, el) => $(el).text()).get().join(' ')
            };
        }
    }

    // 字幕搜索插件
    class SubtitlePlugin extends JavDbPlugin {
        constructor() {
            super();
        }

        handle() {
            if (!this.isDetailPage) return;

            this.addSubtitleButtons();
        }

        addSubtitleButtons() {
            const carNum = this.getPageInfo().carNum;

            // 创建字幕按钮容器
            let container = $('<div class="subtitle-buttons"></div>');

            // 添加字幕按钮
            container.append(`
                <a id="search-subtitle-btn" class="menu-btn">
                    <span>字幕 (SubTitleCat)</span>
                </a>
                <a id="xunLeiSubtitleBtn" class="menu-btn">
                    <span>字幕 (迅雷)</span>
                </a>
            `);

            // 在合适的位置插入按钮
            if (window.location.href.includes('javdb')) {
                $('.tabs.no-bottom').append(container);
            }

            // 绑定点击事件
            $('#search-subtitle-btn').on('click', (e) => {
                utils.openPage(`https://subtitlecat.com/index.php?search=${carNum}`, carNum, false, e);
            });

            $('#xunLeiSubtitleBtn').on('click', () => {
                this.searchXunLeiSubtitle(carNum);
            });
        }

        searchXunLeiSubtitle(carNum) {

            this.gmRequest({
                method: 'GET',
                url: `https://api-shoulei-ssl.xunlei.com/oracle/subtitle?gcid=&cid=&name=${carNum}`,
                onload: (response) => {
                    let data = JSON.parse(response.responseText).data;

            layer.open({
                type: 1,
                        title: '迅雷字幕',
                        content: '<div id="table-container"></div>',
                        area: ['50%', 'auto'],
                        success: (layero) => {
                            if (!data || data.length === 0) {
                                $('#table-container').html('<div class="xunlei-no-subtitle">未找到相关字幕</div>');
                                return;
                            }

                            let tableHtml = `
                                <table class="layui-table">
                                    <tbody>
                            `;

                            data.forEach(item => {
                                tableHtml += `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td><button class="download-btn layui-btn layui-btn-sm" data-url="${item.url}" data-ext="${item.ext}">下载</button></td>
                                    </tr>
                                `;
                            });

                            tableHtml += `</tbody></table>`;
                            $('#table-container').html(tableHtml);

                            // 绑定下载按钮点击事件
                            $('.download-btn').on('click', function() {
                                let url = $(this).data('url');
                                let ext = $(this).data('ext');

                                // 使用GM_xmlhttpRequest下载字幕
                                GM_xmlhttpRequest({
                                    method: 'GET',
                                    url: url,
                                    responseType: 'blob',
                                    onload: function(response) {
                                        // 创建下载链接
                                        const blob = new Blob([response.response], {type: 'application/octet-stream'});
                                        const downloadUrl = URL.createObjectURL(blob);
                                        const a = document.createElement('a');
                                        a.href = downloadUrl;
                                        a.download = `${carNum}.${ext}`;
                                        document.body.appendChild(a);
                                        a.click();
                                        document.body.removeChild(a);
                                        URL.revokeObjectURL(downloadUrl);
                                    }
                                });
                            });
                        }
                    });
                },
                onerror: (error) => {
                    console.error('获取迅雷字幕失败:', error);
                    $('<div class="xunlei-error">获取迅雷字幕失败，请稍后重试</div>').insertAfter('.subtitle-buttons');
                }
            });
        }

        gmRequest(options) {
            GM_xmlhttpRequest({
                method: options.method,
                url: options.url,
                headers: options.headers || {},
                data: options.data || null,
                onload: options.onload,
                onerror: options.onerror || function(error) {
                    console.error('请求失败', error);
                }
            });
        }
    }

    // 字幕猫网站处理插件
    class SubTitleCatPlugin extends BasePlugin {
        handle() {
            if (!window.location.href.includes('subtitlecat.com')) return;

            // 隐藏不需要的元素
            $('.t-banner-inner').hide();
            $('#navbar').hide();

            // 获取搜索关键词并过滤结果
            let searchKey = window.location.href.split('=')[1]?.toLowerCase();
            if (!searchKey) return;

            $('.sub-table tr td a').toArray().forEach(element => {
                let $el = $(element);
                if (!$el.text().toLowerCase().includes(searchKey)) {
                    $el.parent().parent().hide();
                }
            });
        }
    }

    // 演员信息增强插件
    class ActressInfoPlugin extends JavDbPlugin {
        constructor() {
            super();
            this.apiUrl = "https://ja.wikipedia.org/wiki/";
            this.actressInfoCache = new Map();
        }

        handle() {
            // 创建弹出层DOM
            $('body').append('<div class="actress-popup"></div>');

            if (window.location.href.includes('javdb')) {
                // JavDB站点处理
                this.handleJavDbActresses();
            }
        }

        handleJavDbActresses() {
            // 处理演员页面
            if (window.location.href.includes('/actors/')) {
                const actressName = $('.actor-section-name').text().trim();
                const actressId = window.location.pathname.split('/actors/')[1];
                if (actressName) {
                    this.loadAndDisplayActressInfo(actressName, $('.actor-section-name'), actressId);
                }
                return;
            }

            // 处理详情页
            if (!this.isDetailPage) return;

            // 找到所有女演员链接元素
            $('.female').each((i, el) => {
                const $female = $(el);
                const $actressLink = $female.prev('a');

                if ($actressLink.length > 0) {
                    const actressName = $actressLink.text().trim();
                    const href = $actressLink.attr('href') || '';
                    let actressId = '';

                    if (href && href.includes('/actors/')) {
                        actressId = href.split('/actors/')[1];
                    }

                    // 添加hover功能
                    $actressLink.addClass('actress-hover-target');

                    // 绑定鼠标事件，传递演员ID
                    $actressLink.on('mouseenter', (e) => {
                        this.showActressPopup(actressName, e, actressId);
                    });

                    $actressLink.on('mouseleave', () => {
                        $('.actress-popup').hide();
                    });
                }
            });
        }

        async showActressPopup(actressName, event, passedActressId = '') {
            const $popup = $('.actress-popup');

            // 设置初始加载状态
            $popup.html(`
                <div class="actress-loading">加载中...</div>
            `);

            // 获取视窗尺寸
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;

            // 获取目标元素位置和尺寸
            const rect = event.target.getBoundingClientRect();
            const parentWidth = rect.width;

            // 弹出层尺寸
            const popupWidth = 250;
            const popupHeight = 300; // 预估高度，可以根据实际内容调整

            // 计算水平居中位置（相对于父元素居中）
            let left = rect.left + window.scrollX + (parentWidth - popupWidth) / 2;
            // 设置在父元素下方20px
            let top = rect.bottom + window.scrollY + -20;

            // 确保不超出右边界
            if (left + popupWidth > windowWidth + window.scrollX) {
                left = windowWidth - popupWidth + window.scrollX - 20; // 20是边距
            }

            // 确保不超出左边界
            if (left < window.scrollX) {
                left = window.scrollX + 20;
            }

            // 检查下边界，如果超出视窗就显示在元素上方
            if (top + popupHeight > windowHeight + window.scrollY) {
                top = rect.top + window.scrollY - popupHeight - 20;
            }

            // 如果上方也放不下（元素靠近顶部），则尽量显示在视窗中
            if (top < window.scrollY) {
                top = window.scrollY + 10;
            }

            $popup.css({
                left: `${left}px`,
                top: `${top}px`,
                display: 'block'
            });

            // 获取演员ID（从链接中提取或使用传入的ID）
            let actressId = passedActressId;
            if (!actressId) {
                try {
                    // 获取当前元素的链接（需要查找实际的a标签元素）
                    let $actressLink;
                    if ($(event.target).is('a')) {
                        $actressLink = $(event.target);
                    } else {
                        // 可能点击的是内部元素，尝试向上查找a标签
                        $actressLink = $(event.target).closest('a');
                    }

                    if ($actressLink.length > 0) {
                        const href = $actressLink.attr('href');
                        if (href && href.includes('/actors/')) {
                            actressId = href.split('/actors/')[1];
                        }
                    }
                } catch (error) {
                    console.error('获取演员ID失败:', error);
                }
            }

            // 加载演员信息
            try {
                const info = await this.getActressInfo(actressName);
                this.renderActressPopup(actressName, info, actressId);
            } catch (error) {
                $popup.html(`
                    <div class="actress-header">
                        <div class="actress-name">${actressName}</div>
                    </div>
                    <div class="actress-error">获取信息失败</div>
                `);
            }
        }

        async getActressInfo(name) {
            // 检查缓存
            if (this.actressInfoCache.has(name)) {
                return this.actressInfoCache.get(name);
            }

            // 特殊处理一些演员名称
            let searchName = name;
            if (name === '三上悠亞') {
                searchName = '三上悠亜';
            }

            // 从维基百科获取信息
            try {
                const url = this.apiUrl + searchName;
                const response = await this.fetchWithGM(url);
                const doc = new DOMParser().parseFromString(response, 'text/html');
                const $doc = $(doc);

                // 提取信息
                const birthday = $doc.find('tr:has(a[title="誕生日"]) td').text().trim();
                const ageText = $doc.find('th:contains("現年齢")').parent().find('td').text().trim();
                const age = ageText ? parseInt(ageText) + '岁' : '未知';
                const height = $doc.find('tr:has(a[title="身長"]) td').text().trim().split(' ')[0] + 'cm';

                let weight = $doc.find('tr:has(a[title="体重"]) td').text().trim();
                if (weight.includes('/')) {
                    weight = weight.split('/')[1].trim();
                }
                if (weight === '― kg') {
                    weight = '未知';
                }

                const threeSize = $doc.find('a[title="スリーサイズ"]').closest('tr').find('td').text().replace('cm', '').trim();
                const cupSize = $doc.find('th:contains("ブラサイズ")').next('td').contents().first().text().trim();

                const info = {
                    birthday: birthday || '未知',
                    age: age || '未知',
                    height: height || '未知',
                    weight: weight || '未知',
                    threeSize: threeSize || '未知',
                    cupSize: cupSize || '未知',
                    url: url
                };

                // 保存到缓存
                this.actressInfoCache.set(name, info);

                return info;
            } catch (error) {
                console.error('获取演员信息失败:', error);
                return {
                    birthday: '未知',
                    age: '未知',
                    height: '未知',
                    weight: '未知',
                    threeSize: '未知',
                    cupSize: '未知',
                    url: this.apiUrl + searchName
                };
            }
        }

        renderActressPopup(name, info, actressId) {
            const $popup = $('.actress-popup');

            // 构建演员头像URL - 根据JAVDB的URL规则
            let avatarUrl = 'https://c0.jdbstatic.com/avatars/default.jpg';
            if (actressId) {
                // 获取ID的前两个字符，转换为小写
                const firstTwoChars = actressId.slice(0, 2).toLowerCase();
                // 构建最终的URL
                avatarUrl = `https://c0.jdbstatic.com/avatars/${firstTwoChars}/${actressId}.jpg`;
            }

            $popup.html(`
                <div class="actress-header">
                    <img src="${avatarUrl}" class="actress-avatar" onerror="this.src='https://c0.jdbstatic.com/avatars/default.jpg'">
                    <div class="actress-name">${name}</div>
                </div>
                <div class="actress-info-row">
                    <div class="actress-info-label">生日：</div>
                    <div class="actress-info-value">${info.birthday}</div>
                </div>
                <div class="actress-info-row">
                    <div class="actress-info-label">年龄：</div>
                    <div class="actress-info-value">${info.age}</div>
                </div>
                <div class="actress-info-row">
                    <div class="actress-info-label">身高：</div>
                    <div class="actress-info-value">${info.height}</div>
                </div>
                <div class="actress-info-row">
                    <div class="actress-info-label">体重：</div>
                    <div class="actress-info-value">${info.weight}</div>
                </div>
                <div class="actress-info-row">
                    <div class="actress-info-label">三围：</div>
                    <div class="actress-info-value">${info.threeSize}</div>
                </div>
                <div class="actress-info-row">
                    <div class="actress-info-label">罩杯：</div>
                    <div class="actress-info-value">${info.cupSize}</div>
                </div>
            `);
        }

        // 在演员页面显示信息
        async loadAndDisplayActressInfo(actressName, targetElement, actressId) {
            try {
                const info = await this.getActressInfo(actressName);

                // 如果在演员页面，可以直接在页面中显示信息
                // 这里仅作为示例，实际显示方式可以根据需求调整
                if (window.location.href.includes('/actors/')) {
                    console.log('演员页面加载信息:', info);
                }
            } catch (error) {
                console.error('加载演员信息失败:', error);
            }
        }

        fetchWithGM(url) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: url,
                    onload: function(response) {
                        if (response.status >= 200 && response.status < 300) {
                            resolve(response.responseText);
                        } else {
                            reject(new Error('请求失败: ' + response.statusText));
                        }
                    },
                    onerror: function(error) {
                        reject(error);
                    }
                });
            });
        }
    }

    // 初始化工具类
    window.utils = new Utils();
    utils.importResource('https://cdn.jsdelivr.net/npm/layui@2.11.0/dist/css/layui.min.css');
    utils.importResource('https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css');

    // 初始化消息提示
    window.show = {
        info: function(msg, options = {}) {
            Toastify({
                text: msg,
                duration: options.duration || 3000,
                gravity: "top",
                position: 'center',
                style: { background: "#1e88e5" },
                onClick: options.callback
            }).showToast();
        },
        ok: function(msg, options = {}) {
            Toastify({
                text: msg,
                duration: options.duration || 3000,
                gravity: "top",
                position: 'center',
                style: { background: "#43a047" },
                onClick: options.callback
            }).showToast();
        },
        error: function(msg, options = {}) {
            Toastify({
                text: msg,
                duration: options.duration || 3000,
                gravity: "top",
                position: 'center',
                style: { background: "#e53935" },
                onClick: options.callback
            }).showToast();
        }
    };

    // 页面加载完成后初始化插件
    window.onload = function() {
        const pluginManager = new PluginManager(['*']);

        // 注册插件
        if (window.location.hostname.includes('javdb')) {
            pluginManager.register(SubtitlePlugin);
            pluginManager.register(ActressInfoPlugin);
        }

        if (window.location.hostname.includes('subtitlecat')) {
            pluginManager.register(SubTitleCatPlugin);
        }

        // 启动插件
        pluginManager.process().then();
    };
})($, layui);
