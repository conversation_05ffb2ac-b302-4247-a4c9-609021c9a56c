// ==UserScript==
// @name             IT之家
// @description      合并屏蔽特定内容及调整页面布局功能，提升阅读体验。
// <AUTHOR>
// @version          1.0
// @match            http*://*.ithome.com/*
// @run-at           document-start
// @grant            GM_addStyle
// @grant            GM_getValue
// @grant            GM_setValue
// ==/UserScript==
(function () {
	"use strict";

	// 自定义样式定义
	const uiStyles = `

* {
    font-weight: 500 !important;

}

  .-hongbao-container,
  .dajia,
  .fix-top,
  #dt .fr,
  #news > div.fl:first-child {
        display:none;
}
/* 屏蔽按钮和触发区域样式 */
.block-list-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    padding: 5px 10px;
    background: #000;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.trigger-area {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    z-index: 999;
}

.trigger-area:hover .block-list-button {
    opacity: 1;
    pointer-events: auto;
}

.block-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-75%, -50%);
    width: 500px;
    max-width: 90vw;
    height: auto;
    max-height: 80vh;
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 10px 50px rgba(0, 0, 0, 0.25);
    z-index: 10001;
    overflow: auto;
}

.block-dialog::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    z-index: -1;
    backdrop-filter: blur(50px);
}

.block-title-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: none;
    cursor: move;
    user-select: none;
}
.block-title {
margin-top: 2px;
margin-bottom: 2px;
}

.block-close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 16px;
    height: 16px;
    background: black;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.15s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    text-indent: -9999px;
    padding: 0;
    z-index: 1002;
}

.block-close-button:hover {
    background: red;
}

.block-close-line {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 2px;
    background: white;
    transition: background 0.15s ease;
}

.block-close-line-1 {
    transform: translate(-50%, -50%) rotate(45deg);
}

.block-close-line-2 {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.block-list {
    margin: 20px 0 0 0;
    max-height: 220px;
    font-size: 12px;
    padding: 10px;
    background: #00000012;
    border-radius: 8px;
    color: #333;
}

.block-keyword-list {
    margin: 15px 0 0 0;
    max-height: 400px;
    overflow-y: auto;    /* 修正注释格式 */
    padding: 8px;        /* 修正注释格式 */
}

.block-item {
    display: inline-block;
    padding: 4px;    /* 修正注释格式 */
    word-break: break-word;  /* 修正注释格式 */
    max-width: 100%;     /* 修正注释格式 */
    white-space: normal; /* 修正注释格式 */
}

/* 滚动条样式 */
.block-keyword-list::-webkit-scrollbar {
    width: 6px;
}
.block-keyword-list::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 3px;
}
.block-keyword-list::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.2);
    border-radius: 3px;
}
.block-input-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin: 15px 0;
}

.block-input {
    border: 1px solid #dddddd;
    background: #ffffff96;
    border-radius: 8px;
    font-size: 13px;
    padding: 5px 10px;
    width: 180px;
}

.block-input:focus {
    border: 1px solid #999;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1)
}

.block-button {
    border-radius: 8px;
    border: 0px solid black;
    background: #000;
    padding: 5px 10px;
    color: white;
    font-size: 13px;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}
.add_comm {
	margin-top: 5px !important;
	border: 1px #ccc solid !important;
	border-top-width: 1px !important;
	background: #fff !important;
	border-radius: 8px !important;
}
.post_comment h3 {
	border-bottom: none !important;
}
.add_comm input#btnComment {
	border-radius: 8px !important;
}
#dt .fl h1 {
	font-weight: bold;
}
`;

	// 页面布局样式
	const layoutStyles = `
        #dt .fl {
            width: 820px;
            padding-top: 125px;
            margin-left: 100px;
        }

        #tt, #nav, #side_func, .content .shareto, .related_post {
            display: none !important;
        }
        #news {
            padding-top: 125px;
            margin-left: -40px;
        }
        .content .post_content p img {
            margin-bottom: 0;
            margin-top: 0;
            background: url(//img.ithome.com/images/logo.svg) no-repeat center #e7e7e7;
            box-shadow: 0px 1px 10px rgba(0, 0, 0, .1);
            border-radius: 12px !important;
        }
        .ithome_super_player {
            color: transparent !important;
            border-radius: 8px !important;
        }`;

	// 样式注入函数，采用更可靠的多种方法确保样式被应用
	function injectStyles(styles, id) {
		try {
			// 方法1: 使用GM_addStyle
			GM_addStyle(styles);

			// 方法2: 创建样式元素作为备份方法
			const styleEl = document.createElement("style");
			styleEl.id = id;
			styleEl.textContent = styles;

			// 先尝试添加到head
			if (document.head) {
				document.head.appendChild(styleEl);
			} else {
				// 如果head还不存在，使用MutationObserver监听等待head出现
				const headObserver = new MutationObserver(() => {
					if (document.head && !document.getElementById(id)) {
						document.head.appendChild(styleEl);
						headObserver.disconnect();
					}
				});
				headObserver.observe(document.documentElement, { childList: true });

				// 同时将样式元素添加到documentElement作为临时解决方案
				document.documentElement.appendChild(styleEl);
			}

			console.log(`IT之家: ${id} 样式已应用`);
		} catch (error) {
			console.error(`IT之家: ${id} 样式应用失败`, error);
		}
	}

	// 立即注入所有样式
	injectStyles(uiStyles, "ithome-ui-styles");
	injectStyles(layoutStyles, "ithome-layout-styles");

	// 显示通知函数
	function showNotification(message) {
		// 如果body不存在，将消息存储并稍后显示
		if (!document.body) {
			const checkBodyInterval = setInterval(() => {
				if (document.body) {
					clearInterval(checkBodyInterval);
					createNotification(message);
				}
			}, 100);
			return;
		}

		createNotification(message);
	}

	function createNotification(message) {
		const notification = document.createElement("div");
		notification.style.position = "fixed";
		notification.style.bottom = "20px";
		notification.style.left = "20px";
		notification.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
		notification.style.color = "#fff";
		notification.style.padding = "10px 20px";
		notification.style.borderRadius = "8px";
		notification.style.zIndex = "9999";
		notification.style.transition = "opacity 0.3s ease";
		notification.style.opacity = "0";
		notification.textContent = message;

		document.body.appendChild(notification);

		// 显示通知
		setTimeout(() => {
			notification.style.opacity = "1";
		}, 10);

		// 3秒后淡出
		setTimeout(() => {
			notification.style.opacity = "0";
			setTimeout(() => {
				if (document.body.contains(notification)) {
					document.body.removeChild(notification);
				}
			}, 300);
		}, 3000);
	}

	// 安全的存储操作函数
	function safeGetValue(key, defaultValue) {
		try {
			return GM_getValue(key, defaultValue) || defaultValue;
		} catch (error) {
			console.error(`Error getting value for ${key}:`, error);
			return defaultValue;
		}
	}

	function safeSetValue(key, value) {
		try {
			GM_setValue(key, value);
			return true;
		} catch (error) {
			console.error(`Error setting value for ${key}:`, error);
			showNotification(`保存失败: ${error.message}`);
			return false;
		}
	}

	// 获取存储的屏蔽列表
	const blockedKeywords = safeGetValue("blockedKeywords", [
		"华为",
		"荣耀",
		"鸿蒙",
		"卡脖子",
		"航天",
		"比亚迪",
		"问界",
		"我国",
		"封神",
		"流浪地球",
		"huawei",
		"HUAWEI",
		"智界",
		"动画",
		"甘肃",
		"地震",
		"蔚来",
		"小鹏",
		"理想",
		"自主",
		"大飞机",
		"票房",
		"悟空",
		"SU7",
		"赛力斯",
		"龙芯",
		"星闪",
		"OpenHarmony",
		"中国",
		"国产",
		"不良人",
		"黑神话",
		"DeepSeek",
		"宇树",
		"哪吒",
		"中芯国际",
		"致态",
		"长江",
		"大国",
		"尊界",
		"小米汽车",
		"异人之下",
	]);

	// 防抖函数
	function debounce(func, wait) {
		let timeout;
		return function (...args) {
			clearTimeout(timeout);
			timeout = setTimeout(() => func.apply(this, args), wait);
		};
	}

	// 创建触发区域和按钮
	function createBlockButton() {
		// 防止重复创建
		if (document.querySelector(".trigger-area")) {
			return;
		}

		const triggerArea = document.createElement("div");
		triggerArea.className = "trigger-area";

		const button = document.createElement("button");
		button.innerText = "屏蔽列表";
		button.className = "block-list-button";
		button.addEventListener("click", showBlockListDialog);

		triggerArea.appendChild(button);
		document.body.appendChild(triggerArea);
	}

	// 显示屏蔽列表对话框
	function showBlockListDialog() {
		// 检查是否已存在对话框
		if (document.querySelector(".block-dialog")) {
			return;
		}

		const dialog = document.createElement("div");
		dialog.className = "block-dialog";

		// 创建标题栏作为拖动区域
		const titleBar = document.createElement("div");
		titleBar.className = "block-title-bar";

		// 拖动实现
		let isDragging = false;
		let startX, startY;
		let initialLeft, initialTop;

		function startDragging(e) {
			if (e.target === titleBar) {
				isDragging = true;
				startX = e.clientX;
				startY = e.clientY;

				const rect = dialog.getBoundingClientRect();
				initialLeft = rect.left;
				initialTop = rect.top;

				dialog.style.transform = "none";
				dialog.style.left = `${initialLeft}px`;
				dialog.style.top = `${initialTop}px`;
			}
		}

		function doDrag(e) {
			if (!isDragging) return;

			e.preventDefault();
			const deltaX = e.clientX - startX;
			const deltaY = e.clientY - startY;

			dialog.style.left = `${initialLeft + deltaX}px`;
			dialog.style.top = `${initialTop + deltaY}px`;
		}

		function stopDragging() {
			isDragging = false;
		}

		titleBar.addEventListener("mousedown", startDragging);
		document.addEventListener("mousemove", doDrag);
		document.addEventListener("mouseup", stopDragging);

		// 创建关闭按钮
		const closeButton = document.createElement("button");
		closeButton.className = "block-close-button";
		closeButton.innerHTML =
			'<span class="block-close-line block-close-line-1"></span><span class="block-close-line block-close-line-2"></span>';
		closeButton.onclick = () => {
			document.removeEventListener("mousemove", doDrag);
			document.removeEventListener("mouseup", stopDragging);
			document.body.removeChild(dialog);
		};

		// 显示当前屏蔽的关键词列表
		const keywordList = document.createElement("div");
		keywordList.className = "block-list block-keyword-list";
		keywordList.innerHTML = `<h3 class="block-title">屏蔽的关键词:</h3>${
			blockedKeywords.length > 0
				? blockedKeywords
						.map((keyword) => `<span class="block-item">${keyword}</span>`)
						.join("")
				: "<div style='font-style:italic;color:#999'>暂无屏蔽关键词</div>"
		}`;

		// 创建关键词输入区域
		const keywordInputContainer = document.createElement("div");
		keywordInputContainer.className = "block-input-container";

		const keywordInput = document.createElement("input");
		keywordInput.type = "text";
		keywordInput.placeholder = "输入关键词";
		keywordInput.className = "block-input";

		const addKeywordButton = document.createElement("button");
		addKeywordButton.innerText = "添加关键词";
		addKeywordButton.className = "block-button";

		// 添加关键词事件
		addKeywordButton.onclick = () => {
			const newKeyword = keywordInput.value.trim();
			if (newKeyword) {
				if (blockedKeywords.includes(newKeyword)) {
					return;
				}

				blockedKeywords.push(newKeyword);
				if (safeSetValue("blockedKeywords", blockedKeywords)) {
					keywordList.innerHTML = `<h3 class="block-title">屏蔽的关键词:</h3>${blockedKeywords
						.map((keyword) => `<span class="block-item">${keyword}</span>`)
						.join("")}`;
					keywordInput.value = "";
					filterContent();
				}
			}
		};

		// 添加回车键提交
		keywordInput.addEventListener("keypress", (e) => {
			if (e.key === "Enter") {
				addKeywordButton.click();
			}
		});

		// 组装界面元素
		keywordInputContainer.appendChild(keywordInput);
		keywordInputContainer.appendChild(addKeywordButton);

		dialog.appendChild(titleBar);
		dialog.appendChild(closeButton);
		dialog.appendChild(keywordList);
		dialog.appendChild(keywordInputContainer);

		document.body.appendChild(dialog);
	}

	// 检查内容是否应该被屏蔽
	function shouldBlockContent(content) {
		return blockedKeywords.some((keyword) =>
			content.toLowerCase().includes(keyword.toLowerCase())
		);
	}

	// 过滤内容
	function filterContent() {
		let url = window.location.href;
		if (!url.includes("ithome")) {
			return;
		}

		// 优化选择器性能，使用更精确的选择器
		let deleteElements = [
			document.querySelector("#n-p"),
			document.querySelector("#side_func"),
		].filter(Boolean);

		deleteElements.forEach((item) => item && item.remove());

		let box = document.querySelector(".t-b.sel");
		if (!box) return;

		// 使用克隆节点方式保留原始事件监听
		let list = Array.from(document.querySelectorAll(".t-b>.nl > li"));
		let filteredList = list.filter((item) => {
			let a = item.querySelector("a");
			if (!a) return false;
			return !(shouldBlockContent(a.innerText) || a.href.includes("lapin"));
		});

		// 使用文档片段批量操作DOM
		let fragment = document.createDocumentFragment();
		let itemsPerRow = 5;

		while (filteredList.length) {
			let ul = document.createElement("ul");
			ul.className = "nl";
			filteredList.splice(0, itemsPerRow).forEach((item) => {
				ul.appendChild(item.cloneNode(true)); // 使用克隆节点
			});
			fragment.appendChild(ul);
		}

		// 单次DOM操作替换内容
		box.innerHTML = "";
		box.appendChild(fragment);
	}

	// 初始化函数
	function init() {
		if (document.body) {
			createBlockButton();
			filterContent();

			// 添加防抖处理并限制观察范围
			const debouncedFilter = debounce(filterContent, 200);
			const observer = new MutationObserver((mutations) => {
				// 过滤无关的DOM变化
				const hasRelevantChange = mutations.some((mutation) =>
					mutation.target.closest(".t-b.sel, #n-p, #side_func")
				);
				if (hasRelevantChange) {
					debouncedFilter();
				}
			});

			// 精确观察内容区域
			const mainContent = document.querySelector(".t-b.sel") || document.body;
			observer.observe(mainContent, {
				childList: true,
				subtree: true,
				characterData: true,
			});

			// 确保样式已应用
			injectStyles(uiStyles, "ithome-ui-styles");
			injectStyles(layoutStyles, "ithome-layout-styles");
		}
	}

	// 创建一个早期的observer来确保DOM呈现前就能捕获到变化
	const earlyObserver = new MutationObserver(() => {
		// 再次确保样式已应用
		if (!document.getElementById("ithome-ui-styles")) {
			injectStyles(uiStyles, "ithome-ui-styles");
			injectStyles(layoutStyles, "ithome-layout-styles");
		}

		// 检查body是否可用
		if (document.body && !document.querySelector(".trigger-area")) {
			createBlockButton();
			filterContent();

			// 完成初始设置后断开早期观察器
			earlyObserver.disconnect();
		}
	});

	// 观察document的变化，等待body出现
	earlyObserver.observe(document, { childList: true, subtree: true });

	// 如果document已完成加载
	if (
		document.readyState === "interactive" ||
		document.readyState === "complete"
	) {
		// 再次确保样式已应用
		injectStyles(uiStyles, "ithome-ui-styles");
		injectStyles(layoutStyles, "ithome-layout-styles");

		// 如果body已经存在，直接初始化
		if (document.body) {
			init();
			earlyObserver.disconnect();
		}
	} else {
		// 监听DOMContentLoaded事件，再次确保样式应用和初始化
		document.addEventListener("DOMContentLoaded", () => {
			injectStyles(uiStyles, "ithome-ui-styles");
			injectStyles(layoutStyles, "ithome-layout-styles");
			init();
			earlyObserver.disconnect();
		});
	}

	// 最后的保障：监听load事件，确保一切正常运行
	window.addEventListener("load", () => {
		injectStyles(uiStyles, "ithome-ui-styles");
		injectStyles(layoutStyles, "ithome-layout-styles");
		if (document.body && !document.querySelector(".trigger-area")) {
			init();
		}
	});
})();
