// ==UserScript==
// @name         linux.do 回复重构 + 美化合并版
// @namespace    http://tampermonkey.net/
// @version      2.5.6
// @description  合并版：回复重构功能 + 美化布局功能，高速引用加载优化，智能预加载系统，完全按照old.js成功案例实现
// <AUTHOR>
// @match        https://linux.do/*
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // ========== 网站原生加载检测系统 ==========
    let isNativeLoading = false;
    let nativeLoadingTimer = null;

    // 检测网站原生的滚动加载
    function detectNativeLoading() {
        // 检测常见的加载指示器
        const loadingIndicators = [
            '.loading',
            '.spinner',
            '[data-loading]',
            '.discourse-loading',
            '.loading-container'
        ];

        for (const selector of loadingIndicators) {
            if (document.querySelector(selector)) {
                return true;
            }
        }

        // 检测网络请求活动
        if (window.fetch && window.fetch.toString().includes('[native code]')) {
            // 监听原生fetch请求
            const originalFetch = window.fetch;
            let activeRequests = 0;

            window.fetch = function(...args) {
                activeRequests++;
                const promise = originalFetch.apply(this, args);

                promise.finally(() => {
                    activeRequests--;
                    if (activeRequests === 0) {
                        // 所有请求完成后，延迟重置加载状态
                        clearTimeout(nativeLoadingTimer);
                        nativeLoadingTimer = setTimeout(() => {
                            isNativeLoading = false;
                        }, 500);
                    }
                });

                if (activeRequests > 0) {
                    isNativeLoading = true;
                }

                return promise;
            };
        }

        return false;
    }

    // ========== 浏览器检测 ==========
    const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');

    // ========== 配置 - 合并优化 ==========
    const CONFIG = {
        DEBUG: false, // 调试模式已关闭
        // 统一的延迟配置
        PROCESS_DELAY: isFirefox ? 30 : 20, // 减少处理延迟
        MAX_POSTS_PER_BATCH: isFirefox ? 8 : 12, // 增加批处理大小
        BATCH_SIZE: isFirefox ? 2 : 3, // 增加批处理大小
        THROTTLE_DELAY: isFirefox ? 200 : 150, // 减少节流延迟
        FETCH_DELAY: isFirefox ? 30 : 20, // 减少获取延迟
        MAX_FETCH_ATTEMPTS: 3, // 增加重试次数
        QUOTE_STYLE_ID: 'optimized-quote-styles',
        BEAUTIFY_STYLE_ID: 'linux-do-beautify-styles',
        CACHE_TTL: 30 * 60 * 1000, // 增加缓存时间到30分钟
        CACHE_CLEAN_INTERVAL: isFirefox ? 180000 : 240000, // 增加缓存清理间隔
        // 新增性能优化配置
        VIEWPORT_BUFFER: 800, // 增加视口缓冲区
        MAX_CONCURRENT_PROCESSING: 10, // 增加最大并发处理数
        IDLE_TIMEOUT: 50, // 减少空闲超时
        LONG_POST_THRESHOLD: 50, // 长帖子阈值
        // 长内容处理配置
        CONTENT_LENGTH_THRESHOLD: 5000, // 长内容阈值（字符数）
        MAX_QUOTE_LENGTH: 1500, // 引用最大显示长度
        MAX_IMAGES_IN_QUOTE: 10, // 引用中最大图片数量
        ULTRA_LONG_CONTENT_THRESHOLD: 10000, // 超长内容阈值
        // 多线程加载配置 - 优化引用加载速度
        MAX_CONCURRENT_FETCHES: 12, // 增加最大并发网络请求数
        FETCH_TIMEOUT: 5000, // 增加单个请求超时时间
        RETRY_DELAY: 50, // 减少重试延迟
        PARALLEL_BATCH_SIZE: 6, // 增加并行批处理大小
        // 新增：智能加载配置
        SMART_PRELOAD_ENABLED: true, // 启用智能预加载
        VIEWPORT_PRELOAD_DISTANCE: 1000, // 视口预加载距离
        SCROLL_LOAD_PROBABILITY: 0.7, // 滚动时加载概率（从0.1提升到0.7）
        NATIVE_LOADING_DELAY: 500, // 网站原生加载延迟（从2000减少到500）
        AGGRESSIVE_CACHE_ENABLED: true, // 启用激进缓存策略
        WAIT_TIMEOUT: 3000 // 等待embedded-posts加载的超时时间
    };

    // ========== 日志系统 ==========
    const log = CONFIG.DEBUG ? (...args) => console.log(`[${isFirefox ? 'Firefox' : 'Chrome'}]`, ...args) : () => {};

    // ========== 全局错误处理 ==========
    function safeExecute(fn, context = 'Unknown') {
        try {
            return fn();
        } catch (error) {
            log(`安全执行失败 [${context}]:`, error);
            return null;
        }
    }

    async function safeExecuteAsync(fn, context = 'Unknown') {
        try {
            return await fn();
        } catch (error) {
            log(`异步安全执行失败 [${context}]:`, error);
            return null;
        }
    }

    // ========== DOM保护机制已移除 ==========
    // 为了避免与网站原生代码冲突，移除DOM保护机制
    // 改为在我们自己的代码中使用安全的DOM操作



    // ========== 性能监控 ==========
    const performanceStats = {
        quotesProcessed: 0,
        totalProcessingTime: 0,
        averageProcessingTime: 0,
        domOperations: 0,
        cacheHits: 0,
        startTime: performance.now()
    };

    function updatePerformanceStats(processingTime) {
        performanceStats.quotesProcessed++;
        performanceStats.totalProcessingTime += processingTime;
        performanceStats.averageProcessingTime = performanceStats.totalProcessingTime / performanceStats.quotesProcessed;

        if (performanceStats.quotesProcessed % 10 === 0) {
            log(`性能统计: 已处理${performanceStats.quotesProcessed}个引用，平均耗时${performanceStats.averageProcessingTime.toFixed(2)}ms`);
        }
    }

    // ========== 全局状态 ==========
    const postCache = new Map();
    let isProcessing = false;
    let lastProcessTime = 0;
    let rafId = null;
    let processingQueue = [];
    let concurrentProcessing = 0;
    let isLongPost = false;

    // ========== 多线程加载状态管理 ==========
    const fetchPromises = new Map(); // 存储Promise对象
    let activeFetches = 0; // 当前活跃的请求数
    const pendingFetches = []; // 等待处理的请求队列
    const fetchTimeouts = new Map(); // 请求超时管理

    // ========== 优化的样式系统 - 合并重复样式 ==========
    function initStyles() {
        if (document.getElementById(CONFIG.QUOTE_STYLE_ID)) return;

        // 创建其他必要的样式（如果有的话）
        const style = document.createElement('style');
        style.id = CONFIG.QUOTE_STYLE_ID;
        style.textContent = `
            /* 其他必要的样式可以在这里添加 */
        `;

        (document.head || document.documentElement).appendChild(style);
    }



    // ========== 美化样式系统 ==========
    function initBeautifyStyles() {
        // 使用GM_addStyle添加美化样式
        GM_addStyle(`
            /* ========== 楼层号样式管理 ========== */
            /* 为帖子容器设置相对定位 */
            article[id^="post_"] {
                position: relative !important;
            }

            /* 隐藏原始的楼层号显示 */
            article[id^="post_"] .topic-meta-data::after {
                display: none !important;
            }

            /* 楼层号伪元素样式 */
article[id^="post_"][data-floor-number]::after {
	content: attr(data-floor-number) !important;
	position: absolute !important;
	left: 96% !important;
	background: #000 !important;
	color: #fff !important;
	border-radius: 6px !important;
	text-align: center !important;
	display: block !important;
	visibility: visible !important;
	opacity: 0.8 !important;
	pointer-events: none !important;
	top: -5px !important;
	transform: translate(50%, 50%) !important;
	width: 24px !important;
	height: 18px !important;
	font-size: 11px !important;
	line-height: 18px !important;
	box-shadow: 0 1px 6px rgba(0,0,0,0.1) !important;
}

            /* 优化的回复引用样式 */
            .optimized-reply-quote {
                margin-bottom: 10px !important;
                padding: 8px 12px !important;
                background: rgba(0, 0, 0, 0.05) !important;
                border-radius: 10px !important;
                font-size: 14px !important;
                display: block !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 300 !important;
            }

            /* 引用头部样式 */
            .optimized-reply-quote .quote-header {
                font-weight: bold !important;
                margin: 5px !important;
                color: #555 !important;
                display: flex !important;
                align-items: center !important;
            }

            /* 引用头像样式 */
            .optimized-reply-quote .quote-avatar {
                width: 24px !important;
                height: 24px !important;
                border-radius: 4px !important;
                margin-right: 8px !important;
                border: 2px solid rgba(0, 0, 0) !important;
                flex-shrink: 0 !important;
                overflow: hidden;
                line-height: 24;
            }

            /* 引用作者名样式 */
            .optimized-reply-quote .quote-author {
                font-weight: bold !important;
                color: #555 !important;
            }

            /* 引用内容样式 */
            .optimized-reply-quote .quote-content {
                color: #666 !important;
                line-height: 1.4 !important;
                word-wrap: break-word !important;
                padding: 0 0 0 4px !important;
            }

            /* ========== 隐藏原始元素 ========== */

            /* 隐藏嵌入的帖子 */
            .embedded-posts.top,
            article[id^="post_"]:not([data-quote-processed="true"]) .embedded-posts.top {
                display: none !important;
            }

            /* 隐藏回复标签 */
            .reply-to-tab {
                opacity: 0 !important;
                pointer-events: none !important;
                position: absolute !important;
                z-index: -1 !important;
            }

            /* ========== 美化样式 ========== */

            /* 禁用头像滚动跟随功能 */
            .topic-post.sticky-avatar .topic-avatar {
                position: relative !important;
                top: unset !important;
            }

            body::before, body::after {
                content: "";
                display: none !important;
            }
.topic-post-badges,
.desktop-view nav.post-controls .show-replies,
.small-action.topic-post-visited .topic-post-visited-line,
.timeline-container,
#list-area .show-more,
.house-creative,
.topic-list tr.selected td:first-of-type, .topic-list-item.selected td:first-of-type, .latest-topic-list-item.selected, .search-results .fps-result.selected {
	box-shadow: none !important;
  display: none !important;
}

a {
  color: rgba(0, 0, 0, 0.8) !important;
	text-decoration: none !important;
}

a:hover {
	color: #bb4e5b !important;
}

#post_1 .topic-body, #post_1 .topic-avatar,
img.avatar, img.prefix-image {
	border: none !important;
}

img.avatar {
	border-radius: 10px !important;
}

.list-controls .combo-box .combo-box-header {
	border-radius: 12px !important;
  color: #333 !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.select-kit.combo-box.tag-drop .tag-drop-header, .select-kit.combo-box.tag-drop .selected-name {
  color: #333 !important;
}

.select-kit .select-kit-header {
	border: none !important;
	font-size: 14px!important;
	font-weight: bold !important;
	border-radius: 10px !important;
}

.search-menu .search-input, .search-menu-container .search-input {
	border-radius: 12px !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05) !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.header-sidebar-toggle button:focus:hover, .discourse-no-touch .header-sidebar-toggle button:hover {
	background-color: transparent !important;
}

.menu-panel {
	border-radius: 12px !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.1) !important;
	background: rgba(255, 255, 255, 0.7) !important;
  backdrop-filter:blur(20px) saturate(180%) !important;
  margin: 1px 0 !important;
}

.welcome-banner__wrap .results {
	background: transparent !important;
}

.search-random-quick-tip .tip-label {
	background-color: rgba(0, 0, 0, 0.1) !important;backdrop-filter:blur(20px) saturate(180%) !important;
	border-radius: 6px !important;
}

.search-menu .search-link .topic-title, .search-menu-container .search-link .topic-title {
	font-size: 16px !important;
	color: #000000 !important;
	line-height: 2.5;
}

search-menu .search-link:focus, .search-menu .search-link:hover, .search-menu-container .search-link:focus, .search-menu-container .search-link:hover {
	background-color: #E3D6CF70 !important;
}

.search-menu .search-link, .search-menu-container .search-link {
	margin: 20px  !important;
	border-radius: 10px !important;
}


.sidebar-wrapper {
	margin-left: 55px !important;
	border-radius: 12px !important;
	background: rgba(255, 255, 255, 0.7) !important;
}
.sidebar-wrapper .sidebar-container {
	border-right: none !important;
}

.sidebar-section-link-wrapper .sidebar-section-link:focus, .sidebar-section-link-wrapper .sidebar-section-link:hover {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
}
.sidebar-section-wrapper {
	border-bottom: none !important;
}

.sidebar-section-wrapper .sidebar-section-header-wrapper {
	margin-top: 20px !important;
}

.sidebar-section-link-wrapper .sidebar-section-link {
	font-size: 15px;
}

/* 内页帖子卡片式设计 (保留) */
.topic-post, .crawler-post {
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 12px !important;
  margin-bottom: 15px !important;
  border: 1px solid rgba(255, 255, 255, 0.9) !important;
  overflow: hidden !important;
  padding: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  position: relative !important;
}

.topic-post article.boxed, .crawler-post article.boxed {
  border: none !important;
  box-shadow: none !important;
}

.topic-avatar, .crawler-post-meta .creator {
  border-top: none !important;
  padding: 15px 10px 15px 15px !important;
}

.topic-body, .crawler-post .post {
  padding: 15px 15px 10px 0 !important;
}

.topic-meta-data, .crawler-post-meta {
  padding-bottom: 8px !important;
  margin-bottom: 10px !important;
}
.topic-meta-data {
    display: flex !important;
    align-items: flex-start !important;
    flex-direction: column !important;
}

.d-header #site-logo {
	height: var(--d-logo-height)!important;
	width: auto !important;
	max-width: 100% !important;
}

.header-sidebar-toggle button .d-icon {
	width: 100%;
	display: inline-block !important;
	color: rgba(0, 0, 0, 0.8) !important;
}

/* 爬虫模式特殊处理 (保留) */
.crawler-post {
  display: flex !important;
  flex-direction: column !important;
}

.crawler-post-meta {
  display: flex !important;
  align-items: center !important;
  padding: 10px 15px !important;
  background-color: rgba(245, 245, 245, 0.5) !important;
}

.crawler-post .post {
  padding: 15px !important;
}

/* 确保图片视频不溢出 (保留) */
img, video { /* 应用于更广泛的图片/视频，而不仅仅是回复卡片内 */
  max-width: 100% !important;
  height: auto !important;
}


body {
  background: #A8C6CE;
  background-attachment: fixed !important;
}

/*--------标题栏--------*/
.d-header-wrap {
	position: relative !important;
}

.d-header {
	background-color: rgba(255, 255, 255, 0.5)!important;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05) !important
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/*--------主页--------*/
#main-outlet-wrapper {
	background: rgba(255, 255, 255, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.3) !important;
  margin-top: 40px !important;
  border-radius: 12px !important;
  width: 80% !important;
  overflow: hidden;
}

.anon .topic-list-main-link a.title:visited:not(.badge-notification), .anon .topic-list .main-link a.title:visited:not(.badge-notification), .topic-list .anon .main-link a.title:visited:not(.badge-notification) {
	color: #333 !important;
}

.discourse-tag {
	border-radius: 4px;
}

#list-area .show-more {
	top: -30px;
}

#list-area .show-more .alert {
	padding: 5px !important;
	font-size: 14px !important;
	display: block !important;
	background-color: rgba(0, 0, 0, 0.5) !important;
	border-radius: 8px !important;
	color: #fff !important;
	display: block !important;
	text-align: center;
}
.nav-pills > li a.active, .nav-pills > li button.active {
	color: #ca4e4e  !important;
}
/*--------帖子列表布局修改--------*/
/* 修改帖子列表项的布局 */
.topic-list {
  border-collapse: separate !important;
  border-spacing: 0 8px !important;
  padding: 0 20px;
}

.topic-list-item {
	display: flex !important;
	padding: 5px 10px !important;
	align-items: flex-start !important;
	background-color: rgba(255, 255, 255, 0.9) !important;
	margin:25px 12px !important;
	border-radius: 8px !important;
	transition: all 0.2s ease !important;
	position: relative !important;
	border: 1px solid rgba(255, 255, 255, 0.8) !important;
}

.topic-list-item, tr {
	border-bottom: none !important;
}

.topic-list-main-link a.title, .latest-topic-list-item .main-link a.title, .topic-list .main-link a.title {
	font-size: 15px;
}

.topic-list .topic-excerpt {
	font-size: 13px !important;
}

.topic-list .link-bottom-line a.discourse-tag.box {
	font-size: 12px;
}

.badge-category__wrapper .badge-category__name {
	color: #333 !important;
	font-size: 11px !important;
}

.topic-list .link-bottom-line {
	font-size: var(--font-down-1);
	margin-top: 0 !important;
  gap: 0 .5em !important;
  line-height: inherit !important;
}

.topic-list .posters {
	width: auto !important;
}

/* 隐藏原始表格结构 */
.topic-list-item td {
  border: none !important;
  padding: 0 !important;
}

/* 隐藏不需要的单元格 */
.topic-list-item td.posters-names,
.topic-list-item td.activity,
.topic-list-item td.age,
.topic-list-item td.views,
.topic-list-item td.posts-map,
.topic-list-item td.posts {
  display: none !important;
}

/* 显示并定位头像 - 确保在左侧 */
.topic-list-item td.posters {
	display: block !important;
	margin-right: 12px !important;
	position: absolute !important;
	left: 20px !important;
	top: 14px !important;
}

.topic-list-item td.posters a:first-child {
  display: block !important;
}

.topic-list-item td.posters a:not(:first-child) {
  display: none !important;
}

.topic-list-item img.avatar {
	width: 42px !important;
	border-radius: 10px !important;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

/* 创建右侧内容区域 */
.topic-list-item td.main-link {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  padding: 0 !important;
  margin-left: 70px !important; /* 为头像留出空间 */
  position: relative !important;
  line-height: 2;
}

/* 标题样式 */
.topic-list-item .main-link a.title {
	font-size: 15px !important;
	padding: 0 !important;
	font-weight: bold !important;
	color: #333 !important;
	display: block !important;
}

/* 隐藏原始元数据元素，防止它们在被移动到容器前显示在错误位置 */
.topic-list-item td.num.views,
.topic-list-item .topic-list-data.heatmap-low,
.topic-list-item td.num.posts,
.topic-list-item td.num.posts-map.posts,
.topic-list-item td.num.activity,
.topic-list-item td.activity.num.topic-list-data.age {
  visibility: hidden !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
}

/* 当元素被移动到元数据容器后恢复可见性 */
.topic-metadata-container .topic-metadata-item {
  visibility: visible !important;
  position: static !important;
  top: auto !important;
  left: auto !important;
}

/* 通用图标样式 */
.topic-list-item .icon-before:before {
  margin-right: 4px !important;
  font-size: 14px !important;
  color: #333 !important;
  font-family: sans-serif !important;
  display: inline-block !important;
  width: 14px !important;
  height: 14px !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 元数据容器 */
.topic-list-item .topic-metadata-container {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 16px !important;
  position: relative !important;
  width: 100% !important;
  box-sizing: border-box !important;
  left: 0 !important;
  right: 0 !important;
}

/* 元数据项通用样式 */
.topic-list-item .topic-metadata-item {
  display: inline-flex !important;
  align-items: center !important;
  font-size: 13px !important;
  color: #666 !important;
  white-space: nowrap !important;
}

/* 显示作者名字 */
.topic-list-item .topic-author-name {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item .topic-author-name:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示浏览量 */
.topic-list-item td.num.views {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item td.num.views:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示回复数 */
.topic-list-item td.num.posts {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list .num.posts a {
  padding: 0 !important;
}

.topic-list-item td.num.posts:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

/* 显示最后回复时间 */
.topic-list-item td.last-post {
  display: inline-flex !important;
  align-items: center !important;
}

.topic-list-item td.last-post:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.topic-list .num.activity a {
  padding: 0 !important;
}

/* 显示创建/更新日期 */
.topic-list-item td.activity.num.topic-list-data.age {
  display: inline-flex !important;
  align-items: center !important;
  white-space: nowrap !important;
}

.topic-list-item td.activity.num.topic-list-data.age:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
  min-height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
  background-size: 16px 16px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
  opacity: 1 !important;
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 显示分类 */
.topic-list-item td.category {
  display: inline-flex !important;
  align-items: center !important;
  position: absolute !important;
  right: 12px !important;
  bottom: 12px !important;
}

.topic-list-item td.category:before {
  content: "" !important;
  margin-right: 4px !important;
  width: 14px !important;
  height: 14px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M17.63 5.84C17.27 5.33 16.67 5 16 5L5 5.01C3.9 5.01 3 5.9 3 7v10c0 1.1.9 1.99 2 1.99L16 19c.67 0 1.27-.33 1.63-.84L22 12l-4.37-6.16z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.topic-list-item .discourse-tag {
  font-size: 11px !important;
  padding: 2px 6px !important;
  margin-right: 4px !important;
  border-radius: 4px !important;
  background-color: transition !important;
}

.discourse-tag.box {
	background-color: transparent !important;
}


/* 确保表头不受影响 */
#global-notice-alert-global-notice,
.svg-icon-title,
.topic-list-header {
  display: none !important;
}

.topic-statuses {
	display: none !important;
}

/* 确保所有图标不随滚动变化 */
.topic-list-item *:before,
.topic-list-item *::before {
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 确保时间图标正确显示且不随滚动变化 */
td.activity.num.topic-list-data.age:before,
td.activity.num.topic-list-data.age::before {
  content: "" !important;
  margin-right: 4px !important;
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
  min-height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  display: inline-block !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
  background-size: 16px 16px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
  opacity: 1 !important;
  transform: scale(1) !important;
  zoom: 1 !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* 确保元数据容器中的所有元素都有正确的样式 */
.topic-metadata-container .topic-metadata-item {
  margin-right: 0 !important;
  padding: 0 !important;
  position: static !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  top: auto !important;
  display: inline-flex !important;
  align-items: center !important;
  font-size: 12px !important;
  color: #666 !important;
}

/* 添加平滑过渡效果 */
.topic-metadata-container {
  transition: opacity 0.2s ease !important;
}

/* 确保linux-do-beautify类应用后立即隐藏原始元素 */
.linux-do-beautify .topic-list-item td.num.views,
.linux-do-beautify .topic-list-item .topic-list-data.heatmap-low,
.linux-do-beautify .topic-list-item td.num.posts,
.linux-do-beautify .topic-list-item td.num.posts-map.posts,
.linux-do-beautify .topic-list-item td.num.activity,
.linux-do-beautify .topic-list-item td.activity.num.topic-list-data.age {
  visibility: hidden !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
}

/* 为各种图标添加样式 */
.topic-metadata-item .views-icon,
.topic-metadata-item .heatmap-icon,
.topic-metadata-item .posts-icon,
.topic-metadata-item .activity-icon,
.topic-metadata-item .age-icon {
  display: inline-block !important;
  width: 14px !important;
  height: 14px !important;
  margin-right: 4px !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  vertical-align: middle !important;
}

.fk-d-menu__inner-content {
	background-color: transparent;
	border-radius: 12px !important;
  background: rgba(255, 255, 255, 0.8) !important;
	box-shadow: 0 1px 40px rgba(0, 0, 0, 0.1) !important;
	border: 1px solid rgba(255, 255, 255, 0.4) !important;
	backdrop-filter: blur(20px) saturate(180%) !important;
}

.user-card .badge-section .user-badge, .user-card .badge-section .more-user-badges a {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
	font-size: 13px !important;
  border: none !important;
}

.user-card .metadata, .group-card .metadata {
	font-size: 14px  !important;
}

.btn.btn-icon-text.btn-default {
	font-size: 14px !important;
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 10px !important;
}

.user-card, .group-card {
	background: transparent !important;
}

.user-card-avatar .avatar-flair.rounded, .user-profile-avatar .avatar-flair.rounded {
	display: none  !important;
}

.user-card .card-content, .group-card .card-content {
	padding: 30px  !important;
	background: transparent  !important;
}

/* 浏览量图标 */
.topic-metadata-item .views-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E") !important;
}

/* 回复数图标 */
.topic-metadata-item .posts-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E") !important;
}

/* 活动时间图标 */
.topic-metadata-item .activity-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E") !important;
}

/* 创建时间图标 */
.topic-metadata-item .age-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") !important;
}

/* 热度图标 */
.topic-metadata-item .heatmap-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z'/%3E%3C/svg%3E") !important;
}

/*--------- 内页 ----------*/
#topic-title .title-wrapper {
	padding: 0 30px;
}

.topic-category {
	margin: 10px 0;
}

.discourse-tag.box {
	font-size: 12px;
}

.container.posts {
	grid-template-columns: 100% 25% !important;
  padding: 30px;
}

article[id^="post_"] img.avatar {
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

body .reply-to-tab img.avatar {
	border: 1px solid rgba(0, 0, 0, 0.8) !important;
	border-radius: 5px !important;
  margin: 0 10px;
}

.topic-body {
  width: 90% !important;
	border-top: none !important;
}

.topic-body .cooked {
	padding: 0 var(--topic-body-width-padding) !important;
	font-size: 15px !important;
}

.topic-map__contents .topic-map__stats.--single-stat button span, .post-info a, .post-info svg {
	font-size: 12px !important;
	color: var(--tertiary) !important;
}

.topic-map.--op {
	border-top: none !important;
}

.topic-map__stats {
	font-size: 13px;
}

.topic-status-info, .topic-timer-info {
	border-top: none !important;
}

.cooked img:not(.thumbnail, .ytp-thumbnail-image, .emoji), .d-editor-preview img:not(.thumbnail, .ytp-thumbnail-image, .emoji) {
	border-radius: 12px !important;
}

.reply-to-tab {
	display: none !important;

}.topic-meta-data .post-infos {
	align-items: end !important;
  font-size: 13px;
}

aside.quote .title {
	color: inherit !important;
	border-left: 0 !important;
	background-color: transparent !important;
	user-select: none;
}

blockquote {
  margin: 0 !important;
	border-left: none !important;
	background-color: transparent !important;
}

aside.quote {
	margin-bottom: 10px !important;
	padding: 8px 12px !important;
	background-color: rgba(0, 0, 0, 0.05)!important;
	border-left: 0 !important;
	border-radius: 10px !important;
	font-size: 14px !important;
}

.reply-quote {
    margin-bottom: 10px !important;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-left: 0 !important;
    border-radius: 10px;
    font-size: 14px;
}

.cooked img:not(.thumbnail, .ytp-thumbnail-image, .emoji), .d-editor-preview img:not(.thumbnail, .ytp-thumbnail-image, .emoji) {
	border-radius: 6px !important;
}

.names span.first > a {
	color: #000 !important;
}

.second.username > a {
	color: #fff;
	background: rgba(0, 0, 0, .1);
	border-radius: 6px !important;
	font-size: 12px;
	padding: 0 6px;
}

.small-action .small-action-desc {
	box-sizing: border-box;
	display: block !important;
	flex-wrap: wrap;
	color: #000 !important;
	padding: 1em 0 !important;
	width: 100% !important;
	min-width: 0;
	border-top: 1px solid #fff !important;
	text-align: center;
}

.small-action .topic-avatar {
	display: none !important;
}

.lightbox-wrapper .lightbox {
    outline: 5px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 12px !important;
}

aside.onebox {
	border-radius: 12px !important;
}

.topic-avatar .avatar-flair.rounded, .avatar-flair-preview .avatar-flair.rounded, .collapsed-info .user-profile-avatar .avatar-flair.rounded, .user-image .avatar-flair.rounded, .latest-topic-list-item .avatar-flair.rounded {
	display: none !important;
}

.topic-map {
	max-width: 98% !important;
}



.topic-avatar {
	width: 48px !important;
}

.fk-d-menu {
	margin: 10px;
}

.user-card .names__secondary, .user-card [class*="metadata__"], .group-card .names__secondary, .group-card [class*="metadata__"],
.user-card .card-row:not(.first-row), .group-card .card-row:not(.first-row) ,
.user-card .card-content .bio, .group-card .card-content .bio {
	font-size: 14px !important;
}

#user-card img.avatar,
#user-card img.prefix-image {
  border-radius: 16px !important;
	border: 4px solid rgba(0, 0, 0, 0.8) !important;
}

.user-card .first-row .names, .group-card .first-row .names {
	padding-left: 20px !important;
}

.topic-meta-data .user-status-message-wrap img.emoji {

	display: none;
}
/* 楼主标签样式 - 使用伪元素 */
div.topic-avatar::after {
    content: "";
    display: none;
}

div.topic-owner .topic-avatar::after {
	content: "楼主";
	display: block;
	background-color: #e74c3c;
	color: white;
	font-size: 10px;
	font-weight: bold;
	padding: 2px 6px;
	border-radius: 6px;
	text-align: center;
	width: 20px;
	margin: 15px auto 0 auto;
}

.topic-map .--users-summary {
    gap: 0.5em !important;
}

.topic-map__contents .topic-map__stats.--single-stat button span, .post-info a, .post-info svg {
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.5) !important;
	padding: 5px 0 !important;
	display: block !important;
}

.topic-map__contents .topic-map__stats .fk-d-menu__trigger .number {
	color: #000 !important;
}

.topic-map .--users-summary .avatar {
	border-radius: 8px !important;
	border: 2px solid rgba(0, 0, 0, 0.8) !important;
}

.sidebar-section-link-wrapper .sidebar-section-link--active, .sidebar-section-link-wrapper .sidebar-section-link.active {
	background: rgba(0, 0, 0, 0.05) !important;
	border-radius: 8px !important;
}

/* 隐藏原始的楼主标识 */
div.topic-owner .topic-body .contents > .cooked::after {
    display: none !important;
}

code {
	background: rgba(0, 0, 0, 0.07)!important;
	font-size: 14px;
	border-radius: 10px !important;
}

.more-topics__container .topic-list-item td.main-link {
	padding: 0 10px !important;
  margin-left: 0 !important;
}

.post-links-container .track-link span:not(.badge) {
	font-size: 14px;
}

.discourse-reactions-list .reactions .discourse-reactions-list-emoji .emoji {
	width: 0.9em !important;
	height: 0.9em !important;
}

nav.post-controls .actions {
	font-size: 13px  !important;
}

/* --------登录页----------*/
input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"] {
	border-radius: 8px !important;
	background: rgba(255, 255, 255, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 0px 20px rgba(0, 0, 0, 0.05) !important;
}

.login-fullpage #login-buttons .btn-social, .signup-fullpage #login-buttons .btn-social, .invites-show #login-buttons .btn-social, .password-reset-page #login-buttons .btn-social {
	padding: .75em .77em;
	border-radius: 8px !important;
	border: 1px solid rgba(255, 255, 255, 0.5) !important;
	box-shadow: 0 0px 20px rgba(0, 0, 0, 0.05), inset 0px 0px 30px 10px rgba(255, 255, 255, 0.3) !important;
	background: rgba(255, 255, 255, 0.5) !important;
}

.login-fullpage .login-page-cta__existing-account::before, .login-fullpage .login-page-cta__no-account-yet::before, .login-fullpage .signup-page-cta__existing-account::before, .login-fullpage .signup-page-cta__no-account-yet::before, .signup-fullpage .login-page-cta__existing-account::before, .signup-fullpage .login-page-cta__no-account-yet::before, .signup-fullpage .signup-page-cta__existing-account::before, .signup-fullpage .signup-page-cta__no-account-yet::before, .invites-show .login-page-cta__existing-account::before, .invites-show .login-page-cta__no-account-yet::before, .invites-show .signup-page-cta__existing-account::before, .invites-show .signup-page-cta__no-account-yet::before {
	background-color: transparent !important;
}

.btn-primary {
	border-radius: 8px !important;
}

        `);
    }

    // ========== 工具函数 ==========
    function getFloorNumber(element) {
        // 优先从ID中提取
        if (element.id?.includes('_')) {
            const floorFromId = element.id.split('_')[1];
            return floorFromId;
        }

        // 从data-post-id属性中提取
        const postId = element.getAttribute('data-post-id');

        if (postId) {
            let floorNumber = postId.replace('top--', '');

            // 如果包含embedded-posts，尝试提取真实的楼层号
            if (floorNumber.includes('embedded-posts')) {
                // 尝试从embedded-posts__数字中提取数字
                const match = floorNumber.match(/embedded-posts__(\d+)/);
                if (match && match[1]) {
                    floorNumber = match[1];
                } else {
                    // 如果无法提取，返回空字符串
                    floorNumber = '';
                }
            }

            return floorNumber;
        }

        return '';
    }

    // 检测元素是否在视口附近
    function isElementNearViewport(element, buffer = CONFIG.VIEWPORT_BUFFER) {
        if (!element || !element.getBoundingClientRect) return false;

        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight;

        return (
            rect.bottom >= -buffer &&
            rect.top <= windowHeight + buffer
        );
    }

    // 检测是否为长帖子
    function detectLongPost() {
        const posts = document.querySelectorAll('article[id^="post_"]');
        isLongPost = posts.length > CONFIG.LONG_POST_THRESHOLD;
        if (isLongPost) {
            log(`检测到长帖子，共${posts.length}楼，启用性能优化模式`);
        }
        return isLongPost;
    }

    function cachePostContent(postId, content, author, floorNumber, avatarSrc) {
        if (!postId || !content) return;
        postCache.set(postId, {
            content,
            author: author || '未知用户',
            floorNumber: floorNumber || '',
            avatarSrc: avatarSrc || '',
            timestamp: Date.now()
        });
    }

    // ========== 统一的楼层号处理 ==========
    function updateFloorNumbers() {
        document.querySelectorAll('article[id^="post_"]').forEach(function (post) {
            if (!post.hasAttribute('data-floor-number')) { // 检查是否已经添加了楼层号
                const floorNum = getFloorNumber(post);
                if (floorNum) {
                    post.setAttribute('data-floor-number', floorNum);
                    log(`设置楼层号: ${post.id} -> #${floorNum}`);
                }
            }
        });
    }

    // ========== 智能队列处理 ==========
    function addToProcessingQueue(post, priority = 0) {
        if (!post || post.hasAttribute('data-quote-added')) return;

        // 在长帖子模式下，只处理视口附近的帖子
        if (isLongPost && !isElementNearViewport(post)) {
            post.setAttribute('data-quote-deferred', 'true');
            return;
        }

        processingQueue.push({ post, priority, timestamp: Date.now() });
        processingQueue.sort((a, b) => b.priority - a.priority);

        processQueue();
    }

    function processQueue() {
        if (concurrentProcessing >= CONFIG.MAX_CONCURRENT_PROCESSING || processingQueue.length === 0) {
            return;
        }

        const item = processingQueue.shift();
        if (!item || !item.post?.isConnected) {
            processQueue();
            return;
        }

        concurrentProcessing++;
        processPostOptimized(item.post).finally(() => {
            concurrentProcessing--;
            if (processingQueue.length > 0) {
                setTimeout(processQueue, CONFIG.IDLE_TIMEOUT);
            }
        });
    }

    // ========== 新的智能引用处理逻辑 ==========
    async function processPostOptimized(post) {
        try {
            if (!post?.isConnected || post.hasAttribute('data-quote-added')) return;

            // 查找回复标签
            const replyTab = post.querySelector('.reply-to-tab');
            if (!replyTab) return;

            const replyToPostId = replyTab.getAttribute('aria-controls');
            if (!replyToPostId) return;

            log(`智能处理帖子: ${post.id} -> ${replyToPostId}`);

            // 标记为已处理
            post.setAttribute('data-quote-added', 'true');

            // 首先尝试查找页面上已存在的帖子
            const existingPost = findExistingPostInPage(replyToPostId);
            if (existingPost) {
                const postData = extractPostDataFromElement(existingPost);
                if (postData) {
                    createSmartQuoteElement(post, postData, replyToPostId);
                    replyTab.style.display = 'none';
                    return;
                }
            }

            // 如果找不到，尝试触发embedded-posts加载
            const postData = await triggerEmbeddedPostsLoad(post, replyToPostId);
            if (postData) {
                createSmartQuoteElement(post, postData, replyToPostId);
                replyTab.style.display = 'none';
                return;
            }

            log(`无法获取帖子数据: ${replyToPostId}`);
            post.removeAttribute('data-quote-added'); // 允许重试

        } catch (error) {
            log('处理帖子时出错:', error);
            post?.removeAttribute('data-quote-added');
        }
    }

    // 查找页面上已存在的帖子
    function findExistingPostInPage(replyToPostId) {
        // 提取帖子号
        let postNumber = null;

        if (replyToPostId.includes('embedded-posts__top--')) {
            const match = replyToPostId.match(/embedded-posts__top--(\d+)/);
            if (match && match[1]) {
                postNumber = match[1];
            }
        } else if (replyToPostId.includes('embedded-posts__')) {
            const match = replyToPostId.match(/embedded-posts__(\d+)/);
            if (match && match[1]) {
                postNumber = match[1];
            }
        } else if (replyToPostId.startsWith('top--')) {
            postNumber = replyToPostId.replace('top--', '');
        }

        if (!postNumber) return null;

        // 查找对应的帖子
        const selectors = [
            `article[id="post_${postNumber}"]`,
            `article[data-post-number="${postNumber}"]`
        ];

        for (const selector of selectors) {
            const post = document.querySelector(selector);
            if (post) {
                log(`找到已存在的帖子: ${selector}`);
                return post;
            }
        }

        return null;
    }

    // 触发embedded-posts加载
    async function triggerEmbeddedPostsLoad(post, replyToPostId) {
        try {
            log(`触发embedded-posts加载: ${replyToPostId}`);

            // 查找reply-to-tab
            const replyTab = post.querySelector('.reply-to-tab');
            if (!replyTab) {
                log('未找到reply-to-tab');
                return null;
            }

            // 模拟点击以触发加载
            log('模拟点击reply-to-tab');
            replyTab.click();

            // 等待embedded-posts出现
            const embeddedPosts = await waitForEmbeddedPosts(post, CONFIG.WAIT_TIMEOUT || 3000);
            if (!embeddedPosts) {
                log('embedded-posts加载超时');
                return null;
            }

            // 从embedded-posts中提取数据
            const postData = extractPostDataFromElement(embeddedPosts);
            if (postData) {
                log('成功从embedded-posts提取数据');
                // 隐藏embedded-posts
                embeddedPosts.style.display = 'none';
                return postData;
            }

            return null;

        } catch (error) {
            log('触发embedded-posts加载失败:', error);
            return null;
        }
    }

    // 等待embedded-posts出现
    function waitForEmbeddedPosts(post, timeout) {
        return new Promise((resolve) => {
            const startTime = Date.now();

            function checkForEmbeddedPosts() {
                const embeddedPosts = post.querySelector('.embedded-posts.top');
                if (embeddedPosts) {
                    const content = embeddedPosts.querySelector('.cooked');
                    if (content && content.textContent.trim()) {
                        log('找到embedded-posts内容');
                        resolve(embeddedPosts);
                        return;
                    }
                }

                if (Date.now() - startTime > timeout) {
                    log('等待embedded-posts超时');
                    resolve(null);
                    return;
                }

                setTimeout(checkForEmbeddedPosts, 100);
            }

            checkForEmbeddedPosts();
        });
    }



    // ========== 新的数据提取函数 ==========
    // 从帖子元素中提取数据
    function extractPostDataFromElement(postElement) {
        try {
            // 提取用户名
            const usernameElement = postElement.querySelector('.names .username, [data-user-card], .trigger-user-card');
            let username = '未知用户';
            if (usernameElement) {
                username = usernameElement.getAttribute('data-user-card') ||
                          usernameElement.textContent?.trim() ||
                          '未知用户';
                // 清理用户名中的特殊字符
                username = username.replace(/^[@#]+/, '').trim();
            }

            // 提取头像
            const avatarElement = postElement.querySelector('.topic-avatar img.avatar, img.avatar');
            const avatarSrc = avatarElement?.getAttribute('src') || '';

            // 提取内容
            const contentElement = postElement.querySelector('.cooked');
            if (!contentElement) {
                log('未找到帖子内容元素');
                return null;
            }

            // 克隆内容以避免修改原始DOM
            const contentClone = contentElement.cloneNode(true);

            // 清理不需要的元素
            const elementsToRemove = contentClone.querySelectorAll('script, style, .lightbox-wrapper, .onebox, .cooked-selection-barrier');
            elementsToRemove.forEach(el => el.remove());

            // 获取清理后的内容
            let finalContent = contentClone.innerHTML.trim();

            // 如果HTML内容为空，尝试获取文本内容
            if (!finalContent) {
                finalContent = contentClone.textContent || contentClone.innerText || '';
            }

            // 如果还是为空，返回提示
            if (!finalContent) {
                finalContent = '无法获取内容';
            }

            // 截断过长的内容
            const textContent = contentClone.textContent || contentClone.innerText || '';
            if (textContent.length > (CONFIG.MAX_QUOTE_LENGTH || 400)) {
                finalContent = textContent.substring(0, CONFIG.MAX_QUOTE_LENGTH || 400) + '...';
            }

            // 提取楼层号
            const postNumber = postElement.getAttribute('data-post-number') ||
                              postElement.id?.replace('post_', '') ||
                              '?';

            return {
                username: username,
                avatarSrc: avatarSrc,
                content: finalContent,
                postNumber: postNumber
            };

        } catch (error) {
            log('提取帖子数据时出错:', error);
            return null;
        }
    }

    // 创建智能引用元素
    function createSmartQuoteElement(post, postData, replyToPostId) {
        try {
            const quoteDiv = document.createElement('div');
            quoteDiv.className = 'optimized-reply-quote';

            // 创建头部
            const headerDiv = document.createElement('div');
            headerDiv.className = 'quote-header';

            if (postData.avatarSrc) {
                const avatar = document.createElement('img');
                avatar.src = postData.avatarSrc;
                avatar.alt = postData.username;
                avatar.className = 'quote-avatar';
                headerDiv.appendChild(avatar);
            }

            const authorSpan = document.createElement('span');
            authorSpan.textContent = postData.username;
            authorSpan.className = 'quote-author';
            headerDiv.appendChild(authorSpan);

            const floorSpan = document.createElement('span');
            floorSpan.textContent = '#' + postData.postNumber;
            floorSpan.style.cssText = 'background: #007cbb; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px; margin-left: 8px;';
            headerDiv.appendChild(floorSpan);

            // 创建内容
            const contentDiv = document.createElement('div');
            contentDiv.className = 'quote-content';
            contentDiv.innerHTML = postData.content || '内容为空';

            quoteDiv.appendChild(headerDiv);
            quoteDiv.appendChild(contentDiv);

            // 插入引用
            const postBody = post.querySelector('.topic-body .cooked');
            if (postBody) {
                postBody.insertBefore(quoteDiv, postBody.firstChild);
                log(`成功添加智能引用: ${post.id} -> ${replyToPostId}`);
            } else {
                // 尝试其他位置
                const topicBody = post.querySelector('.topic-body');
                if (topicBody) {
                    topicBody.insertBefore(quoteDiv, topicBody.firstChild);
                    log(`使用备用位置插入智能引用: ${post.id} -> ${replyToPostId}`);
                }
            }

        } catch (error) {
            log('创建智能引用元素时出错:', error);
        }
    }

    // ========== 智能用户名提取函数 ==========
    function extractCleanUsername(parentPost, context = 'unknown') {
        // 尝试多种选择器策略
        const selectors = [
            '.names .username',
            '[data-user-card]',
            '.username',
            '.names a',
            'a[href*="/u/"]',
            '.trigger-user-card'
        ];

        let foundElement = null;

        for (const selector of selectors) {
            foundElement = parentPost.querySelector(selector);
            if (foundElement) {
                break;
            }
        }

        if (!foundElement) {
            return '未知用户';
        }

        // 按优先级提取用户名
        let username = '';

        // 1. 优先使用 data-user-card 属性
        const dataUserCard = foundElement.getAttribute('data-user-card');
        if (dataUserCard) {
            if (!dataUserCard.includes('embedded-posts') && !dataUserCard.includes('#')) {
                username = dataUserCard.trim();
                return username;
            }
        }

        // 2. 尝试获取纯文本内容
        const textContent = foundElement.textContent || '';

        // 强化的清理逻辑，移除所有可能的前缀
        let cleanText = textContent
            .replace(/#embedded-posts__\d+\s*/g, '') // 移除 #embedded-posts__数字
            .replace(/embedded-posts__\d+\s*/g, '') // 移除 embedded-posts__数字（无#）
            .replace(/#embedded-posts[^\s]*\s*/g, '') // 移除其他 embedded-posts 相关内容
            .replace(/embedded-posts[^\s]*\s*/g, '') // 移除其他 embedded-posts 相关内容（无#）
            .replace(/^#\d+\s*/, '') // 移除开头的 #数字
            .replace(/^\s*#\s*/, '') // 移除开头的单独 #
            .trim();

        // 如果清理后仍然包含问题字符，进行更深度清理
        if (cleanText.includes('embedded-posts') || cleanText.includes('#')) {
            // 更激进的清理
            cleanText = cleanText
                .replace(/.*embedded-posts.*?\s+/g, '') // 移除包含embedded-posts的整个部分
                .replace(/.*#.*?\s+/g, '') // 移除包含#的整个部分
                .replace(/[#]/g, '') // 移除所有#字符
                .trim();
        }

        if (cleanText && cleanText.length > 0 && cleanText.length < 50) {
            username = cleanText;
            return username;
        }

        // 3. 尝试从子元素中获取
        const usernameSpan = foundElement.querySelector('.username, .trigger-user-card');
        if (usernameSpan) {
            const spanText = usernameSpan.textContent?.trim();
            if (spanText && !spanText.includes('embedded-posts') && !spanText.includes('#')) {
                username = spanText;
                return username;
            }
        }

        return '未知用户';
    }

    function extractPostData(parentPost, replyToPostId) {
        const content = parentPost.querySelector('.cooked');

        // 使用智能用户名提取函数
        const username = extractCleanUsername(parentPost, `extractPostData-${replyToPostId}`);

        const avatar = parentPost.querySelector('.topic-avatar img.avatar');

        if (!content) {
            return null;
        }

        // 安全地获取内容，避免使用innerHTML
        const contentClone = content.cloneNode(true);
        const result = {
            content: contentClone,
            author: username,
            floorNumber: getFloorNumber(parentPost) || replyToPostId.replace('top--', ''),
            avatarSrc: avatar?.getAttribute('src') || ''
        };

        return result;
    }

    // ========== 处理嵌入内容 ==========
    function processEmbeddedReply(embeddedPosts, post, replyToPostId) {
        try {
            embeddedPosts.style.display = 'none';

            const postData = extractPostData(embeddedPosts, replyToPostId);
            if (!postData) {
                log('嵌入帖子缺少必要元素');
                return;
            }

            // 获取楼层号 - 修复embedded-posts问题
            const dataPostId = embeddedPosts.getAttribute('data-post-id');
            if (dataPostId) {
                // 清理楼层号，移除embedded-posts前缀
                let cleanFloorNumber = dataPostId.replace('top--', '');

                // 如果包含embedded-posts，尝试提取真实的楼层号
                if (cleanFloorNumber.includes('embedded-posts')) {
                    // 尝试从embedded-posts__数字中提取数字
                    const match = cleanFloorNumber.match(/embedded-posts__(\d+)/);
                    if (match && match[1]) {
                        cleanFloorNumber = match[1];
                    } else {
                        // 如果无法提取，使用replyToPostId作为备选
                        const backupFloorNumber = replyToPostId.replace('top--', '');
                        if (!backupFloorNumber.includes('embedded-posts')) {
                            cleanFloorNumber = backupFloorNumber;
                        } else {
                            // 最后的备选：不显示楼层号
                            cleanFloorNumber = '';
                        }
                    }
                }

                postData.floorNumber = cleanFloorNumber;
            }

            if (replyToPostId) {
                cachePostContent(replyToPostId, ...Object.values(postData));
            }

            // 使用超高速引用创建
            createQuoteInstantly(post, postData);

            embeddedPosts.classList.add('processed');
            post.setAttribute('data-quote-processed', 'true');

            const replyTab = post.querySelector('.reply-to-tab');
            if (replyTab) replyTab.style.display = 'none';

        } catch (error) {
            log('处理嵌入回复时出错:', error);
        }
    }

    // ========== DOM操作安全检查 ==========
    function safeInsertBefore(parent, newNode, referenceNode) {
        try {
            if (!parent || !newNode || !parent.isConnected) {
                log('DOM插入失败：父节点或新节点无效');
                return false;
            }

            // 检查referenceNode是否有效
            if (referenceNode && referenceNode.nodeType === Node.ELEMENT_NODE && referenceNode.parentNode === parent) {
                parent.insertBefore(newNode, referenceNode);
            } else {
                // 如果referenceNode无效，直接appendChild
                parent.appendChild(newNode);
            }
            return true;
        } catch (error) {
            log('DOM插入出错，尝试appendChild:', error);
            try {
                parent.appendChild(newNode);
                return true;
            } catch (appendError) {
                log('appendChild也失败:', appendError);
                return false;
            }
        }
    }

    // ========== 多线程异步加载系统 ==========

    // 多线程请求管理器
    function createFetchRequest(replyToPostId, post) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`请求超时: ${replyToPostId}`));
            }, CONFIG.FETCH_TIMEOUT);

            fetchTimeouts.set(replyToPostId, timeoutId);

            try {
                const replyTab = post.querySelector('.reply-to-tab');
                if (!replyTab?.isConnected) {
                    clearTimeout(timeoutId);
                    fetchTimeouts.delete(replyToPostId);
                    reject(new Error('Reply tab not found'));
                    return;
                }

                // 创建兼容的点击事件
                let clickEvent;
                try {
                    clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true
                    });
                } catch (e) {
                    clickEvent = document.createEvent('MouseEvent');
                    clickEvent.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
                }

                replyTab.dispatchEvent(clickEvent);

                // 异步检查结果 - 优化重试机制
                let attempts = 0;
                const maxAttempts = Math.min(CONFIG.MAX_FETCH_ATTEMPTS * 2, 10); // 增加最大尝试次数但设置上限

                const checkForEmbeddedPosts = () => {
                    if (!post?.isConnected) {
                        clearTimeout(timeoutId);
                        fetchTimeouts.delete(replyToPostId);
                        reject(new Error(`Post disconnected: ${replyToPostId}`));
                        return;
                    }

                    if (++attempts > maxAttempts) {
                        clearTimeout(timeoutId);
                        fetchTimeouts.delete(replyToPostId);
                        log(`达到最大尝试次数，优雅降级: ${replyToPostId}`);

                        // 优雅降级：尝试从现有内容中提取
                        const embeddedPosts = post.querySelector('.embedded-posts.top');
                        if (embeddedPosts) {
                            const postData = extractPostData(embeddedPosts, replyToPostId);
                            if (postData) {
                                cachePostContent(replyToPostId, ...Object.values(postData));
                                resolve(postData);
                                return;
                            }
                        }

                        // 如果无法提取，则静默失败而不是抛出错误
                        resolve(null);
                        return;
                    }

                    const embeddedPosts = post.querySelector('.embedded-posts.top');
                    const cookedElement = embeddedPosts?.querySelector('.cooked');
                    if (cookedElement && cookedElement.textContent?.trim()) {
                        clearTimeout(timeoutId);
                        fetchTimeouts.delete(replyToPostId);

                        embeddedPosts.style.display = 'none';
                        const postData = extractPostData(embeddedPosts, replyToPostId);

                        if (postData) {
                            // 获取楼层号 - 修复embedded-posts问题
                            const dataPostId = embeddedPosts.getAttribute('data-post-id');
                            if (dataPostId) {
                                // 清理楼层号，移除embedded-posts前缀
                                let cleanFloorNumber = dataPostId.replace('top--', '');

                                // 如果包含embedded-posts，尝试提取真实的楼层号
                                if (cleanFloorNumber.includes('embedded-posts')) {
                                    // 尝试从embedded-posts__数字中提取数字
                                    const match = cleanFloorNumber.match(/embedded-posts__(\d+)/);
                                    if (match && match[1]) {
                                        cleanFloorNumber = match[1];
                                    } else {
                                        // 如果无法提取，使用replyToPostId作为备选
                                        const backupFloorNumber = replyToPostId.replace('top--', '');
                                        if (!backupFloorNumber.includes('embedded-posts')) {
                                            cleanFloorNumber = backupFloorNumber;
                                        } else {
                                            // 最后的备选：不显示楼层号
                                            cleanFloorNumber = '';
                                        }
                                    }
                                }

                                postData.floorNumber = cleanFloorNumber;
                            }

                            cachePostContent(replyToPostId, ...Object.values(postData));
                            resolve(postData);
                        } else {
                            // 如果提取失败，继续尝试而不是立即失败
                            setTimeout(checkForEmbeddedPosts, CONFIG.FETCH_DELAY * Math.min(attempts, 3));
                        }
                    } else {
                        // 动态调整检查间隔，避免过于频繁的检查
                        const delay = CONFIG.FETCH_DELAY * Math.min(Math.ceil(attempts / 3), 5);
                        setTimeout(checkForEmbeddedPosts, delay);
                    }
                };

                checkForEmbeddedPosts();

            } catch (error) {
                clearTimeout(timeoutId);
                fetchTimeouts.delete(replyToPostId);
                reject(error);
            }
        });
    }

    // 高速并发请求管理器 - 已废弃，使用新的智能引用逻辑
    async function fetchParentPostConcurrent_DEPRECATED(replyToPostId, post) {
        return await safeExecuteAsync(async () => {
            // 如果检测到网站原生加载，短暂延迟处理
            if (isNativeLoading) {
                log(`检测到网站原生加载，短暂延迟请求: ${replyToPostId}`);
                post.removeAttribute('data-quote-added');
                setTimeout(() => {
                    if (post.isConnected && !isNativeLoading) {
                        fetchParentPostConcurrent(replyToPostId, post);
                    }
                }, CONFIG.NATIVE_LOADING_DELAY); // 从2000ms减少到500ms
                return;
            }

        // 智能滚动时加载策略 - 大幅提高加载概率
        if (window.isScrolling && Math.random() > CONFIG.SCROLL_LOAD_PROBABILITY) {
            log(`滚动时跳过网络请求: ${replyToPostId}`);
            post.removeAttribute('data-quote-added');
            return;
        }

        // 检查是否已有相同的请求在进行
        if (fetchPromises.has(replyToPostId)) {
            log(`复用现有请求: ${replyToPostId}`);
            try {
                const postData = await fetchPromises.get(replyToPostId);
                createQuoteInstantly(post, postData);
                return;
            } catch (error) {
                log(`复用请求失败: ${replyToPostId}`, error);
                post.removeAttribute('data-quote-added');
                return;
            }
        }

        // 优化的并发限制 - 长帖子也允许更多并发
        const maxConcurrent = isLongPost ? 6 : CONFIG.MAX_CONCURRENT_FETCHES;
        if (activeFetches >= maxConcurrent) {
            log(`达到并发限制，加入等待队列: ${replyToPostId}`);
            pendingFetches.push({ replyToPostId, post, timestamp: Date.now() });
            return;
        }

        // 开始新的请求
        activeFetches++;
        const fetchPromise = createFetchRequest(replyToPostId, post);
        fetchPromises.set(replyToPostId, fetchPromise);

        log(`开始高速并发请求 (${activeFetches}/${maxConcurrent}): ${replyToPostId}`);

        try {
            const postData = await fetchPromise;

            // 检查返回的数据是否有效
            if (postData && postData.content) {
                createQuoteInstantly(post, postData);
                post.setAttribute('data-quote-processed', 'true');

                // 隐藏reply-to-tab
                const replyTab = post.querySelector('.reply-to-tab');
                if (replyTab) replyTab.style.display = 'none';

                log(`成功处理引用: ${replyToPostId}`);
            } else {
                // 如果返回null或无效数据，静默处理
                log(`引用数据无效，静默跳过: ${replyToPostId}`);
                post.removeAttribute('data-quote-added');
            }

        } catch (error) {
            // 检查错误类型，避免对正常的null返回进行重试
            if (error && error.message && !error.message.includes('Max attempts reached')) {
                log(`并发请求失败: ${replyToPostId}`, error);

                // 优化重试策略
                const retryCount = post.getAttribute('data-retry-count') || '0';
                const retryNum = parseInt(retryCount);
                const maxRetries = Math.min(CONFIG.MAX_FETCH_ATTEMPTS, 2); // 减少重试次数避免过多错误

                if (retryNum < maxRetries && post.isConnected && !isNativeLoading) {
                    post.setAttribute('data-retry-count', (retryNum + 1).toString());
                    log(`准备重试请求 ${replyToPostId} (第${retryNum + 1}次)`);

                    // 指数退避重试策略
                    const retryDelay = CONFIG.RETRY_DELAY * Math.pow(2, retryNum);
                    setTimeout(() => {
                        if (post.isConnected && !isNativeLoading) {
                            post.removeAttribute('data-quote-added');
                            fetchParentPostConcurrent(replyToPostId, post);
                        }
                    }, retryDelay);
                } else {
                    post.removeAttribute('data-quote-added');
                    post.removeAttribute('data-retry-count');
                    log(`请求 ${replyToPostId} 达到最大重试次数，静默跳过`);
                }
            } else {
                // 对于达到最大尝试次数的情况，静默处理
                post.removeAttribute('data-quote-added');
                post.removeAttribute('data-retry-count');
                log(`请求 ${replyToPostId} 静默跳过`);
            }
        } finally {
            // 清理并处理等待队列
            activeFetches--;
            fetchPromises.delete(replyToPostId);

            // 快速处理等待队列
            if (pendingFetches.length > 0 && activeFetches < maxConcurrent) {
                const nextRequest = pendingFetches.shift();
                if (nextRequest.post.isConnected && !isNativeLoading) {
                    // 立即处理下一个请求，不延迟
                    setTimeout(() => {
                        fetchParentPostConcurrent(nextRequest.replyToPostId, nextRequest.post);
                    }, 10); // 最小延迟
                }
            }
        }
        }, `fetchParentPostConcurrent-${replyToPostId}`);
    }

    // 批量并行处理
    async function processBatchConcurrent(posts) {
        const batches = [];
        for (let i = 0; i < posts.length; i += CONFIG.PARALLEL_BATCH_SIZE) {
            batches.push(posts.slice(i, i + CONFIG.PARALLEL_BATCH_SIZE));
        }

        for (const batch of batches) {
            const promises = batch.map(post => {
                const replyTab = post.querySelector('.reply-to-tab');
                const replyToPostId = replyTab?.getAttribute('aria-controls');

                if (replyToPostId && !post.hasAttribute('data-quote-added')) {
                    return processPostOptimized(post);
                }
                return Promise.resolve();
            });

            // 等待当前批次完成再处理下一批次
            await Promise.allSettled(promises);

            // 批次间的短暂延迟，避免过载
            if (batches.indexOf(batch) < batches.length - 1) {
                await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY));
            }
        }
    }



    // ========== 多线程状态监控 ==========
    function getMultiThreadStatus() {
        return {
            activeFetches,
            maxConcurrentFetches: CONFIG.MAX_CONCURRENT_FETCHES,
            pendingFetches: pendingFetches.length,
            activePromises: fetchPromises.size,
            activeTimeouts: fetchTimeouts.size,
            cacheSize: postCache.size
        };
    }

    // 调试函数 - 仅在DEBUG模式下可用
    if (CONFIG.DEBUG) {
        window.getMultiThreadStatus = getMultiThreadStatus;
        window.clearAllFetches = () => {
            fetchPromises.clear();
            fetchTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            fetchTimeouts.clear();
            pendingFetches.length = 0;
            activeFetches = 0;
            log('已清理所有多线程请求状态');
        };
    }







    // ========== 页面切换检测 ==========
    function handlePageChange() {
        log('检测到页面变化，立即重置并处理');

        // 确保DOM准备好
        if (!document.body) {
            log('DOM未准备好，延迟处理页面变化');
            setTimeout(handlePageChange, 50);
            return;
        }

        // 清除所有已处理标记
        const allTopicItems = document.querySelectorAll('.topic-list-item[data-metadata-processed]');
        allTopicItems.forEach(item => {
            item.removeAttribute('data-metadata-processed');
        });
        // 立即处理所有元素
        processAllExistingTopicItems();
    }

    // ========== 美化功能：添加楼主标识 ==========
    function addTopicOwnerClass() {
        // 检查当前是否在帖子详情页
        if (!window.location.pathname.startsWith('/t/')) {
            return;
        }

        log('添加楼主标识...');

        // 获取第一个帖子（通常是楼主帖）
        const firstPost = document.querySelector('#post_1');
        if (!firstPost) {
            log('未找到第一个帖子');
            return;
        }

        // 检查是否已有topic-owner类
        if (firstPost.classList.contains('topic-owner')) {
            log('第一个帖子已有topic-owner类');
            return;
        }

        // 获取第一个帖子的作者
        const firstPostAuthor = firstPost.querySelector('[data-user-card]');
        if (!firstPostAuthor) {
            log('未找到第一个帖子的作者信息');
            return;
        }

        const firstPostUsername = firstPostAuthor.getAttribute('data-user-card');

        // 获取主题作者（从主题元数据中获取）
        const topicAuthor = document.querySelector('.topic-meta-data [data-user-card]');
        if (!topicAuthor) {
            log('未找到主题作者信息');
            return;
        }

        const topicUsername = topicAuthor.getAttribute('data-user-card');

        // 比较作者是否一致，如果一致则添加topic-owner类
        if (firstPostUsername === topicUsername) {
            firstPost.classList.add('topic-owner');
            log(`为第一个帖子添加topic-owner类 (作者: ${firstPostUsername})`);
        } else {
            log(`第一个帖子作者 (${firstPostUsername}) 与主题作者 (${topicUsername}) 不一致`);
        }
    }

    // ========== 智能批量处理 ==========
    function processAllPosts() {
        const now = Date.now();
        if (isProcessing || (now - lastProcessTime < CONFIG.THROTTLE_DELAY)) return;

        isProcessing = true;
        lastProcessTime = now;

        try {
            // 检测是否为长帖子
            detectLongPost();

            const allPosts = Array.from(document.querySelectorAll('article[id^="post_"]:not([data-quote-added]):not([data-quote-deferred])'));
            if (!allPosts.length) {
                isProcessing = false;
                return;
            }

            // 在长帖子模式下，优先处理视口内的帖子
            let postsToProcess;
            if (isLongPost) {
                const viewportPosts = allPosts.filter(post => isElementNearViewport(post));
                const nearbyPosts = allPosts.filter(post => !isElementNearViewport(post, 0) && isElementNearViewport(post));
                postsToProcess = [...viewportPosts, ...nearbyPosts.slice(0, CONFIG.MAX_POSTS_PER_BATCH - viewportPosts.length)];
                log(`长帖子模式: 处理${viewportPosts.length}个视口内帖子，${nearbyPosts.length}个附近帖子`);
            } else {
                postsToProcess = allPosts.slice(0, CONFIG.MAX_POSTS_PER_BATCH);
            }

            log(`处理帖子批次: ${postsToProcess.length}/${allPosts.length}`);

            // 对于长帖子，使用并发批量处理
            if (isLongPost && postsToProcess.length > CONFIG.PARALLEL_BATCH_SIZE) {
                log('检测到长帖子，启用并发批量处理');
                processBatchConcurrent(postsToProcess).then(() => {
                    log('并发批量处理完成');
                }).catch(error => {
                    log('并发批量处理出错:', error);
                });
            } else {
                // 使用队列系统处理帖子
                postsToProcess.forEach((post, index) => {
                    const priority = isElementNearViewport(post, 0) ? 2 : (isElementNearViewport(post) ? 1 : 0);
                    setTimeout(() => {
                        addToProcessingQueue(post, priority);
                    }, index * (CONFIG.PROCESS_DELAY / CONFIG.BATCH_SIZE));
                });
            }

            isProcessing = false;

        } catch (error) {
            log('处理帖子批次出错:', error);
            isProcessing = false;
        }
    }

    // ========== 处理延迟的帖子 ==========
    function processDeferredPosts() {
        if (isLongPost) {
            const deferredPosts = document.querySelectorAll('article[data-quote-deferred="true"]');
            deferredPosts.forEach(post => {
                if (isElementNearViewport(post)) {
                    post.removeAttribute('data-quote-deferred');
                    addToProcessingQueue(post, 1);
                }
            });
        }
    }

    // ========== 节流函数 ==========
    function throttle(func, delay) {
        let lastCall = 0;
        let timeoutId = null;

        return function(...args) {
            const now = Date.now();
            const remaining = delay - (now - lastCall);

            if (remaining <= 0) {
                clearTimeout(timeoutId);
                timeoutId = null;
                lastCall = now;
                func.apply(this, args);
            } else if (!timeoutId) {
                timeoutId = setTimeout(() => {
                    lastCall = Date.now();
                    timeoutId = null;
                    func.apply(this, args);
                }, remaining);
            }
        };
    }



    // ========== DOM观察器 ==========
    function initObserver() {
        // 确保DOM准备好后再初始化观察器
        if (!document.body) {
            log('DOM未准备好，延迟初始化观察器');
            setTimeout(initObserver, 50);
            return;
        }

        // 创建专门的美化功能观察器 - 最高优先级
        const beautifyObserver = new MutationObserver(function(mutations) {
            // 立即处理美化功能，不使用任何延迟或节流
            for (const mutation of mutations) {
                if (mutation.type !== 'childList' || mutation.addedNodes.length === 0) continue;

                Array.from(mutation.addedNodes).forEach(node => {
                    if (node.nodeType !== Node.ELEMENT_NODE) return;

                    // 立即检查并处理topic-list-item
                    if (node.matches?.('.topic-list-item')) {
                        log('立即处理新的topic-list-item');
                        processTopicItemImmediately(node);
                    } else if (node.querySelector?.('.topic-list-item')) {
                        log('立即处理容器中的topic-list-item');
                        const topicItems = node.querySelectorAll('.topic-list-item');
                        topicItems.forEach(item => processTopicItemImmediately(item));
                    }
                });
            }
        });

        // 回复重构的节流处理 - 较低优先级
        const throttledReplyHandler = throttle((mutations) => {
            const hasNewPosts = mutations.some(mutation =>
                mutation.type === 'childList' &&
                Array.from(mutation.addedNodes).some(node =>
                    node.nodeType === Node.ELEMENT_NODE &&
                    (node.matches?.('article[id^="post_"]') ||
                     node.querySelector?.('article[id^="post_"]'))
                )
            );

            if (hasNewPosts) {
                log('检测到新帖子');
                updateFloorNumbers();
                processAllPosts();
            }
        }, CONFIG.THROTTLE_DELAY);

        // 创建回复重构观察器
        const replyObserver = new MutationObserver(throttledReplyHandler);

        const mainContent = document.querySelector('#main-outlet') || document.body;

        if (mainContent) {
            log('成功初始化DOM观察器');
            // 美化观察器 - 最高优先级，立即响应
            beautifyObserver.observe(mainContent, {
                childList: true,
                subtree: true,
                attributes: false,
                characterData: false
            });

            // 回复重构观察器 - 较低优先级
            replyObserver.observe(mainContent, {
                childList: true,
                subtree: true,
                attributes: false,
                characterData: false
            });
        } else {
            log('未找到主要内容容器，延迟重试');
            setTimeout(initObserver, 100);
        }
    }

    // ========== 立即处理单个topic-list-item ==========
    function processTopicItemImmediately(item) {
        // 如果已经处理过，跳过
        if (item.dataset.metadataProcessed === 'true' || item.querySelector('.topic-metadata-container')) {
            return;
        }

        // 立即标记为正在处理
        item.dataset.metadataProcessed = 'processing';

        const mainLink = item.querySelector('td.main-link');
        if (!mainLink) {
            item.dataset.metadataProcessed = 'true';
            return;
        }

        // 立即创建元数据容器
        const metadataContainer = document.createElement('div');
        metadataContainer.className = 'topic-metadata-container';
        metadataContainer.style.opacity = '1'; // 直接显示，不要渐入效果

        let hasMetadata = false;

        // 立即获取所有信息
        const userLink = item.querySelector('a[data-user-card]');
        const viewsElement = item.querySelector('td.num.views');
        const heatmapElement = item.querySelector('.topic-list-data.heatmap-low');
        const postsElement = item.querySelector('td.num.posts, td.num.posts-map.posts');
        const ageElement = item.querySelector('td.activity.num.topic-list-data.age');
        const activityElement = item.querySelector('td.num.activity');

        // 批量创建所有元数据元素
        if (userLink) {
            const authorName = userLink.getAttribute('data-user-card');
            if (authorName) {
                const authorElement = document.createElement('div');
                authorElement.className = 'topic-author-name topic-metadata-item';
                authorElement.textContent = authorName;
                metadataContainer.appendChild(authorElement);
                hasMetadata = true;
            }
        }

        if (viewsElement && viewsElement.textContent.trim()) {
            const viewsMetadata = document.createElement('div');
            viewsMetadata.className = 'topic-metadata-item views-metadata';
            const viewsIcon = document.createElement('span');
            viewsIcon.className = 'views-icon';
            viewsMetadata.appendChild(viewsIcon);
            viewsMetadata.appendChild(document.createTextNode(viewsElement.textContent));
            metadataContainer.appendChild(viewsMetadata);
            hasMetadata = true;
        }

        if (heatmapElement && heatmapElement.textContent.trim()) {
            const heatmapMetadata = document.createElement('div');
            heatmapMetadata.className = 'topic-metadata-item heatmap-metadata';
            const heatmapIcon = document.createElement('span');
            heatmapIcon.className = 'heatmap-icon';
            heatmapMetadata.appendChild(heatmapIcon);
            heatmapMetadata.appendChild(document.createTextNode(heatmapElement.textContent));
            metadataContainer.appendChild(heatmapMetadata);
            hasMetadata = true;
        }

        if (postsElement && postsElement.textContent.trim()) {
            const postsMetadata = document.createElement('div');
            postsMetadata.className = 'topic-metadata-item posts-metadata';
            const postsIcon = document.createElement('span');
            postsIcon.className = 'posts-icon';
            postsMetadata.appendChild(postsIcon);
            postsMetadata.appendChild(document.createTextNode(postsElement.textContent));
            metadataContainer.appendChild(postsMetadata);
            hasMetadata = true;
        }

        if (ageElement && ageElement.textContent.trim()) {
            const timeMetadata = document.createElement('div');
            timeMetadata.className = 'topic-metadata-item age-metadata';
            const ageIcon = document.createElement('span');
            ageIcon.className = 'age-icon';
            timeMetadata.appendChild(ageIcon);
            timeMetadata.appendChild(document.createTextNode(ageElement.textContent));
            metadataContainer.appendChild(timeMetadata);
            hasMetadata = true;
        } else if (activityElement && activityElement.textContent.trim()) {
            const timeMetadata = document.createElement('div');
            timeMetadata.className = 'topic-metadata-item activity-metadata';
            const activityIcon = document.createElement('span');
            activityIcon.className = 'activity-icon';
            timeMetadata.appendChild(activityIcon);
            timeMetadata.appendChild(document.createTextNode(activityElement.textContent));
            metadataContainer.appendChild(timeMetadata);
            hasMetadata = true;
        }

        // 立即插入DOM
        if (hasMetadata) {
            mainLink.appendChild(metadataContainer);
            item.dataset.metadataProcessed = 'true';
            log('元数据容器立即创建完成');
        } else {
            item.dataset.metadataProcessed = 'true';
        }
    }

    // ========== 安全的DOM操作函数 ==========
    function safeAddClassToBody() {
        if (document.body) {
            document.body.classList.add('linux-do-beautify');
            return true;
        }
        return false;
    }

    function waitForBody(callback) {
        if (document.body) {
            callback();
        } else {
            // 如果body还没准备好，等待一下
            setTimeout(() => waitForBody(callback), 10);
        }
    }

    // ========== 立即处理所有现有元素 ==========
    function processAllExistingTopicItems() {
        // 确保DOM已准备好
        if (!document.body) {
            log('DOM未准备好，延迟处理');
            setTimeout(processAllExistingTopicItems, 50);
            return;
        }

        const allTopicItems = document.querySelectorAll('.topic-list-item');
        log(`立即处理${allTopicItems.length}个现有的topic-list-item`);

        allTopicItems.forEach(item => {
            processTopicItemImmediately(item);
        });
    }

    // ========== 初始化 ==========
    function init() {
        log(`${isFirefox ? 'Firefox' : 'Chrome'}合并优化版开始初始化`);

        // 最高优先级：初始化网站原生加载检测
        detectNativeLoading();

        // 立即初始化样式
        initStyles();
        initBeautifyStyles();

        // 安全地添加美化类到body
        waitForBody(() => {
            safeAddClassToBody();
            log('成功添加美化类到body');
        });

        // 初始化页面路径跟踪
        window.lastPath = window.location.pathname;

        // 等待DOM准备好后处理现有元素
        waitForBody(() => {
            processAllExistingTopicItems();
        });

        window.isScrolling = false;

        // 等待DOM准备好后初始化观察器
        waitForBody(() => {
            initObserver();
        });

        // 简化的初始化处理
        const initProcess = () => {
            updateFloorNumbers();
            waitForBody(() => {
                processAllExistingTopicItems(); // 再次确保处理
            });
            addTopicOwnerClass();
            setTimeout(() => {
                processAllPosts();
            }, CONFIG.PROCESS_DELAY);
        };

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initProcess);
        } else {
            initProcess();
        }

        window.addEventListener('load', () => {
            updateFloorNumbers();
            waitForBody(() => {
                processAllExistingTopicItems(); // 确保load后也处理
            });
            addTopicOwnerClass();

            // 使用超级预加载系统
            setTimeout(() => {
                superPreloadQuotes();
            }, 50); // 减少延迟，更快启动

            processAllPosts();
        });

        // 页面切换时的处理（监听历史记录变化）
        window.addEventListener('popstate', () => {
            log('检测到历史记录变化');
            setTimeout(() => {
                handlePageChange();
            }, 50); // 减少延迟
        });

        // 监听路径变化（用于单页应用导航）
        let lastUrl = location.href;
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                log('检测到URL变化');
                setTimeout(() => {
                    handlePageChange();
                }, 50); // 减少延迟
            }
        }).observe(document, {subtree: true, childList: true});

        // 简化的DOMContentLoaded处理
        document.addEventListener('DOMContentLoaded', function() {
            log('DOMContentLoaded - 立即处理');
            safeAddClassToBody();
            processAllExistingTopicItems();
            addTopicOwnerClass();
        });

        // 优化滚动事件处理 - 平衡性能与加载速度
        const scrollThrottle = isFirefox ? 300 : 250; // 适中的节流延迟
        const scrollEndDelay = isFirefox ? 600 : 500; // 适中的延迟
        let lastScrollTime = 0;
        let scrollProcessingLock = false;

        window.addEventListener('scroll', throttle(() => {
            const now = Date.now();

            // 如果检测到网站原生加载，完全停止处理
            if (isNativeLoading) {
                log('检测到网站原生加载，跳过滚动处理');
                return;
            }

            // 防止重复处理
            if (scrollProcessingLock) {
                return;
            }

            // 在长帖子模式下，适度减少处理频率
            if (isLongPost && now - lastScrollTime < 1000) {
                return;
            }

            lastScrollTime = now;
            window.isScrolling = true;
            scrollProcessingLock = true;

            // 延迟处理，让网站原生功能优先
            setTimeout(() => {
                try {
                    // 再次检查是否有原生加载
                    if (isNativeLoading) {
                        return;
                    }

                    // 只在非长帖子模式下更新楼层号
                    if (!isLongPost) {
                        updateFloorNumbers();
                    }

                    // 智能预加载 - 在滚动时预加载视口附近的引用
                    if (CONFIG.SMART_PRELOAD_ENABLED && Math.random() < 0.3) {
                        smartViewportPreload();
                    }

                    // 在长帖子模式下，适度频率处理延迟帖子
                    if (isLongPost && Math.random() < 0.2) {
                        processDeferredPosts();
                    }
                } finally {
                    scrollProcessingLock = false;
                }
            }, 50); // 减少延迟

            clearTimeout(window.scrollEndTimer);
            window.scrollEndTimer = setTimeout(() => {
                window.isScrolling = false;

                // 如果检测到网站原生加载，跳过处理
                if (isNativeLoading) {
                    return;
                }

                // 优化处理频率
                if (!isLongPost) {
                    // 减少延迟，提高响应速度
                    setTimeout(() => {
                        if (!isNativeLoading) {
                            processAllPosts();
                            waitForBody(() => {
                                processAllExistingTopicItems();
                            });
                        }
                    }, 100);
                } else if (Math.random() < 0.1) {
                    // 长帖子模式下10%的概率处理
                    setTimeout(() => {
                        if (!isNativeLoading) {
                            processAllPosts();
                        }
                    }, 200);
                }

                // 滚动结束后进行智能预加载
                if (CONFIG.SMART_PRELOAD_ENABLED) {
                    setTimeout(() => {
                        if (!isNativeLoading) {
                            smartViewportPreload();
                        }
                    }, 300);
                }
            }, scrollEndDelay);
        }, scrollThrottle), { passive: true });

        // 定期维护
        setInterval(() => {
            // 清理过期缓存
            const now = Date.now();
            for (const [key, value] of postCache.entries()) {
                if (now - value.timestamp > CONFIG.CACHE_TTL) {
                    postCache.delete(key);
                }
            }

            // 清理处理队列中的过期项目
            processingQueue = processingQueue.filter(item =>
                item.post?.isConnected && (now - item.timestamp < 30000)
            );

            // 清理超时的请求
            for (const [replyToPostId, timeoutId] of fetchTimeouts.entries()) {
                if (timeoutId && (now - timeoutId > CONFIG.FETCH_TIMEOUT * 2)) {
                    clearTimeout(timeoutId);
                    fetchTimeouts.delete(replyToPostId);
                    fetchPromises.delete(replyToPostId);
                    log(`清理超时请求: ${replyToPostId}`);
                }
            }

            // 清理等待队列中的过期项目
            const validPendingFetches = pendingFetches.filter(item =>
                item.post?.isConnected && (now - (item.timestamp || 0) < 30000)
            );
            if (validPendingFetches.length !== pendingFetches.length) {
                pendingFetches.length = 0;
                pendingFetches.push(...validPendingFetches);
                log(`清理等待队列: ${pendingFetches.length}个有效请求`);
            }

            // 保护楼层号
            updateFloorNumbers();

            // 定期检查未处理的元素（在长帖子模式下降低频率）
            if (document.body && (!isLongPost || Math.random() < 0.2)) {
                processAllExistingTopicItems();
            }

            // 在长帖子模式下，定期处理延迟的帖子
            if (isLongPost) {
                processDeferredPosts();
            }
        }, CONFIG.CACHE_CLEAN_INTERVAL);

        window.addEventListener('beforeunload', () => {
            if (rafId) cancelAnimationFrame(rafId);
        });

        log(`${isFirefox ? 'Firefox' : 'Chrome'}合并优化版初始化完成`);
    }

    // ========== 智能全局错误处理 ==========
    window.addEventListener('error', function(event) {
        if (!event.error) return;

        const errorMsg = event.error.message || '';
        const errorStack = event.error.stack || '';

        // 检查是否是我们脚本相关的错误
        const isOurError = errorStack.includes('linux.do') ||
                          errorStack.includes('optimized-reply-quote') ||
                          errorStack.includes('userscript') ||
                          errorStack.includes('tampermonkey');

        // 检查是否是变量未定义错误（如 't is undefined'）
        if (errorMsg.includes('is undefined') || errorMsg.includes('is not defined')) {
            // 如果是我们脚本相关的，进行处理
            if (isOurError) {
                log('检测到脚本相关的变量未定义错误:', errorMsg);
                event.preventDefault();
                event.stopPropagation();
                return;
            }
            // 如果不是我们的错误，完全不干预，让网站自行处理
            return;
        }

        // 检查是否是网站原生代码的错误
        if (!isOurError) {
            // 如果是网站原生错误，暂停我们的处理以避免冲突
            if (errorMsg.includes('TypeError') || errorMsg.includes('ReferenceError')) {
                log('检测到网站原生错误，暂停脚本处理:', errorMsg);
                isNativeLoading = true;
                clearTimeout(nativeLoadingTimer);
                nativeLoadingTimer = setTimeout(() => {
                    isNativeLoading = false;
                    log('恢复脚本处理');
                }, 3000);
            }
            return; // 不干预网站原生错误
        }

        // 只处理我们脚本相关的DOM错误
        if (isOurError && errorMsg) {
            log('检测到脚本相关错误:', errorMsg);

            const errorMsgLower = errorMsg.toLowerCase();
            if (errorMsgLower.includes('insertbefore') ||
                errorMsgLower.includes('argument 1 is not an object') ||
                errorMsgLower.includes('dom') ||
                errorMsgLower.includes('node')) {
                log('检测到DOM操作错误，尝试恢复:', errorMsg);

                // 清理可能有问题的DOM操作队列
                domOperationQueue.length = 0;
                if (domBatchTimer) {
                    clearTimeout(domBatchTimer);
                    domBatchTimer = null;
                }

                // 重置模板
                quoteTemplate = null;

                // 暂停处理，避免与网站冲突
                isNativeLoading = true;
                setTimeout(() => {
                    isNativeLoading = false;
                    try {
                        initQuoteTemplate();
                        log('模板重新初始化完成');
                    } catch (reinitError) {
                        log('模板重新初始化失败:', reinitError);
                    }
                }, 1000);

                // 防止错误传播
                event.preventDefault();
                event.stopPropagation();
            }
        }

        // 尝试恢复基本功能（只对我们的错误）
        if (isOurError && document.body && !document.body.classList.contains('linux-do-beautify')) {
            safeAddClassToBody();
        }
    });

    // 添加未捕获的Promise错误处理
    window.addEventListener('unhandledrejection', function(event) {
        log('未捕获的Promise错误:', event.reason);
        if (event.reason && event.reason.message &&
            event.reason.message.toLowerCase().includes('insertbefore')) {
            log('检测到Promise中的DOM错误');
            event.preventDefault(); // 防止错误传播
        }
    });

    // ========== 启动 ==========
    try {
        init();
    } catch (error) {
        log('初始化错误:', error);
        // 尝试基本的样式初始化
        setTimeout(() => {
            try {
                initStyles();
                initBeautifyStyles();
                if (document.body) {
                    safeAddClassToBody();
                }
            } catch (e) {
                log('恢复初始化失败:', e);
            }
        }, 1000);
    }






    // ========== 革命性高性能引用系统 ==========

    // 模板缓存系统
    let quoteTemplate = null;

    // 初始化模板
    function initQuoteTemplate() {
        if (quoteTemplate && quoteTemplate.content) return quoteTemplate;

        try {
            // 检查浏览器是否支持template元素
            if (typeof HTMLTemplateElement === 'undefined') {
                log('浏览器不支持template元素，使用降级方案');
                throw new Error('不支持template元素');
            }

            quoteTemplate = document.createElement('template');
            if (!quoteTemplate) {
                throw new Error('无法创建template元素');
            }

            // 使用安全的DOM操作创建模板内容
            const blockquote = document.createElement('blockquote');
            blockquote.className = 'optimized-reply-quote';

            const header = document.createElement('div');
            header.className = 'quote-header';

            const avatar = document.createElement('img');
            avatar.className = 'quote-avatar';
            avatar.style.display = 'none';
            avatar.alt = '';

            const author = document.createElement('span');
            author.className = 'quote-author';

            const content = document.createElement('div');
            content.className = 'quote-content';

            header.appendChild(avatar);
            header.appendChild(author);
            blockquote.appendChild(header);
            blockquote.appendChild(content);

            quoteTemplate.content.appendChild(blockquote);

            // 验证模板内容
            if (!quoteTemplate.content || !quoteTemplate.content.querySelector('.optimized-reply-quote')) {
                throw new Error('模板内容无效');
            }

            return quoteTemplate;
        } catch (error) {
            log('初始化模板失败，使用简化方案:', error);
            // 不使用template，直接返回null让createElement使用简化创建方式
            quoteTemplate = null;
            return null;
        }
    }

    // 批量DOM操作队列
    let domOperationQueue = [];
    let domBatchTimer = null;

    // 智能内容处理函数
    function processQuoteContent(rawContent) {
        if (!rawContent) return '';

        try {
            // 如果rawContent是DOM元素，直接处理
            if (rawContent instanceof Element) {
                const contentClone = rawContent.cloneNode(true);

                // 移除可能导致问题的元素
                const problematicElements = contentClone.querySelectorAll('script, style, iframe, object, embed');
                problematicElements.forEach(el => el.remove());

                // 获取纯文本长度进行检查
                const textContent = contentClone.textContent || contentClone.innerText || '';
                const textLength = textContent.length;

                // 根据内容长度采用不同策略
                if (textLength <= 500) {
                    // 短内容：保持原样
                    return contentClone;
                } else if (textLength <= CONFIG.MAX_QUOTE_LENGTH) {
                    // 中等长度：保持原样但可能需要优化
                    return contentClone;
                } else {
                    // 长内容：智能截断
                    log(`检测到长内容 (${textLength}字符)，进行智能截断`);

                    // 创建截断后的容器
                    const truncatedDiv = document.createElement('div');
                    const maxLength = CONFIG.MAX_QUOTE_LENGTH;
                    let currentLength = 0;

                    // 优先按段落截断
                    const paragraphs = contentClone.querySelectorAll('p, div');
                    if (paragraphs.length > 0) {
                        for (const p of paragraphs) {
                            const pText = p.textContent || '';
                            if (currentLength + pText.length > maxLength) {
                                break;
                            }
                            truncatedDiv.appendChild(p.cloneNode(true));
                            currentLength += pText.length;
                        }
                    } else {
                        // 按字符截断
                        const fullText = textContent;
                        let cutPoint = maxLength;

                        // 寻找最近的句号
                        for (let i = maxLength; i > maxLength * 0.8; i--) {
                            if (fullText[i] === '。' || fullText[i] === '.' || fullText[i] === '！' || fullText[i] === '!') {
                                cutPoint = i + 1;
                                break;
                            }
                        }

                        truncatedDiv.textContent = fullText.substring(0, cutPoint);
                    }

                    if (currentLength >= maxLength) {
                        const ellipsis = document.createElement('span');
                        ellipsis.style.cssText = 'color: #999; font-style: italic;';
                        ellipsis.textContent = '... (内容过长已截断)';
                        truncatedDiv.appendChild(ellipsis);
                    }

                    return truncatedDiv;
                }
            }

            // 如果是字符串，转换为纯文本
            if (typeof rawContent === 'string') {
                const textOnly = rawContent.replace(/<[^>]*>/g, '');
                const div = document.createElement('div');
                div.textContent = textOnly.length > CONFIG.MAX_QUOTE_LENGTH ?
                    textOnly.substring(0, CONFIG.MAX_QUOTE_LENGTH) + '...' :
                    textOnly;
                return div;
            }

            return null;
        } catch (error) {
            log('处理引用内容时出错:', error);
            // 降级到纯文本处理
            const div = document.createElement('div');
            if (typeof rawContent === 'string') {
                const textOnly = rawContent.replace(/<[^>]*>/g, '');
                div.textContent = textOnly.length > CONFIG.MAX_QUOTE_LENGTH ?
                    textOnly.substring(0, CONFIG.MAX_QUOTE_LENGTH) + '...' :
                    textOnly;
            } else {
                div.textContent = '内容处理失败';
            }
            return div;
        }
    }

    // 虚拟引用对象
    class VirtualQuote {
        constructor(post, content, author, floorNumber, avatarSrc) {
            this.post = post;
            this.content = processQuoteContent(content); // 使用智能内容处理

            // 对作者名进行额外的清理，确保移除所有问题前缀
            this.author = this.cleanAuthorName(author);
            this.floorNumber = floorNumber;
            this.avatarSrc = avatarSrc;
            this.element = null;
        }

        // 清理作者名的专用函数
        cleanAuthorName(author) {
            if (!author || typeof author !== 'string') {
                return '未知用户';
            }

            // 强化的清理逻辑
            let cleanAuthor = author
                .replace(/#embedded-posts__\d+\s*/g, '') // 移除 #embedded-posts__数字
                .replace(/embedded-posts__\d+\s*/g, '') // 移除 embedded-posts__数字（无#）
                .replace(/#embedded-posts[^\s]*\s*/g, '') // 移除其他 embedded-posts 相关内容
                .replace(/embedded-posts[^\s]*\s*/g, '') // 移除其他 embedded-posts 相关内容（无#）
                .replace(/^#\d+\s*/, '') // 移除开头的 #数字
                .replace(/^\s*#\s*/, '') // 移除开头的单独 #
                .trim();

            // 如果仍然包含问题字符，进行更深度清理
            if (cleanAuthor.includes('embedded-posts') || cleanAuthor.includes('#')) {
                // 更激进的清理
                cleanAuthor = cleanAuthor
                    .replace(/.*embedded-posts.*?\s+/g, '') // 移除包含embedded-posts的整个部分
                    .replace(/.*#.*?\s+/g, '') // 移除包含#的整个部分
                    .replace(/[#]/g, '') // 移除所有#字符
                    .trim();
            }

            // 最终验证
            if (!cleanAuthor || cleanAuthor.length === 0 || cleanAuthor.length >= 50) {
                return '未知用户';
            }

            return cleanAuthor;
        }

        createElement() {
            if (this.element) return this.element;

            // 优先使用简化的直接创建方式，避免template和cloneNode的问题
            try {
                const quoteElement = document.createElement('blockquote');
                quoteElement.className = 'optimized-reply-quote';

                // 创建头部
                const header = document.createElement('div');
                header.className = 'quote-header';

                // 创建头像（如果有）
                if (this.avatarSrc) {
                    try {
                        const avatar = document.createElement('img');
                        avatar.className = 'quote-avatar';
                        avatar.src = this.avatarSrc;
                        avatar.alt = this.author || '';
                        avatar.style.cssText = 'width: 24px; height: 24px; border-radius: 4px; margin-right: 8px; border: 2px solid rgba(0, 0, 0); flex-shrink: 0;';
                        header.appendChild(avatar);
                    } catch (avatarError) {
                        log('创建头像失败:', avatarError);
                    }
                }

                // 创建作者名
                const authorSpan = document.createElement('span');
                authorSpan.className = 'quote-author';
                // 再次确保作者名是清理过的
                const displayText = this.cleanAuthorName(this.author);
                authorSpan.textContent = displayText;
                header.appendChild(authorSpan);

                // 创建内容
                const contentDiv = document.createElement('div');
                contentDiv.className = 'quote-content';

                // 安全设置内容
                try {
                    if (this.content instanceof Element) {
                        // 如果内容是DOM元素，直接添加
                        contentDiv.appendChild(this.content.cloneNode(true));
                    } else if (this.content && typeof this.content === 'string') {
                        // 如果是字符串，转换为纯文本
                        const textContent = this.content.replace(/<[^>]*>/g, '');
                        contentDiv.textContent = textContent;
                    } else {
                        contentDiv.textContent = '内容无效';
                    }
                } catch (error) {
                    log('设置引用内容失败，使用纯文本:', error);
                    try {
                        const textContent = (this.content || '').toString().replace(/<[^>]*>/g, '');
                        contentDiv.textContent = textContent || '内容无法显示';
                    } catch (textError) {
                        log('设置纯文本也失败:', textError);
                        contentDiv.textContent = '内容无法显示';
                    }
                }

                // 组装元素
                quoteElement.appendChild(header);
                quoteElement.appendChild(contentDiv);

                this.element = quoteElement;
                return quoteElement;
            } catch (error) {
                log('直接创建引用元素失败:', error);

                // 最简化的降级方案
                try {
                    const basicQuote = document.createElement('div');
                    basicQuote.className = 'optimized-reply-quote';

                    const safeAuthor = this.cleanAuthorName(this.author || '未知用户').replace(/[<>]/g, '');
                    const safeContent = (this.content || '').replace(/<[^>]*>/g, '').substring(0, 500);
                    const headerText = safeAuthor;

                    // 使用安全的DOM操作
                    const header = document.createElement('div');
                    header.className = 'quote-header';
                    header.textContent = headerText;

                    const content = document.createElement('div');
                    content.className = 'quote-content';
                    content.textContent = safeContent + (safeContent.length >= 500 ? '...' : '');

                    basicQuote.appendChild(header);
                    basicQuote.appendChild(content);
                    this.element = basicQuote;
                    return basicQuote;
                } catch (fallbackError) {
                    log('创建简化版本也失败:', fallbackError);
                    // 最后的文本降级方案
                    const textQuote = document.createElement('div');
                    textQuote.className = 'optimized-reply-quote';
                    textQuote.textContent = `引用 ${this.cleanAuthorName(this.author || '未知用户')}: [内容无法显示]`;
                    this.element = textQuote;
                    return textQuote;
                }
            }
        }

        insertIntoPost() {
            try {
                const regularContents = this.post.querySelector('.regular.contents');
                if (!regularContents || regularContents.hasAttribute('data-quote-added')) return false;

                regularContents.setAttribute('data-quote-added', 'true');

                const postContent = regularContents.querySelector('.cooked') || regularContents;
                if (!postContent || !postContent.isConnected) return false;

                const quoteElement = this.createElement();
                if (!quoteElement) return false;

                // 安全的DOM插入
                try {
                    // 使用DocumentFragment优化插入
                    const fragment = document.createDocumentFragment();
                    fragment.appendChild(quoteElement);

                    // 使用安全插入函数
                    if (!safeInsertBefore(postContent, fragment, postContent.firstChild)) {
                        log('安全插入失败，尝试直接appendChild');
                        postContent.appendChild(quoteElement);
                    }
                } catch (insertError) {
                    log('DOM插入失败，尝试直接appendChild:', insertError);
                    // 降级处理：直接appendChild
                    postContent.appendChild(quoteElement);
                }

                return true;
            } catch (error) {
                log('insertIntoPost出错:', error);
                return false;
            }
        }
    }

    // 批量DOM操作处理器
    function addToDOMQueue(virtualQuote) {
        domOperationQueue.push(virtualQuote);

        if (domBatchTimer) clearTimeout(domBatchTimer);

        domBatchTimer = setTimeout(() => {
            processDOMBatch();
        }, 5); // 5ms批处理，极快响应
    }

    function processDOMBatch() {
        if (domOperationQueue.length === 0) return;

        const batch = domOperationQueue.splice(0, 10); // 每批最多10个

        // 使用requestAnimationFrame确保在下一帧执行
        requestAnimationFrame(() => {
            let successCount = 0;
            let errorCount = 0;

            batch.forEach((virtualQuote, index) => {
                try {
                    if (!virtualQuote.post?.isConnected) {
                        log(`跳过已断开连接的帖子 ${index}`);
                        return;
                    }

                    // 检查内容长度，对超长内容进行特殊处理
                    const contentLength = virtualQuote.content?.length || 0;
                    if (contentLength > CONFIG.ULTRA_LONG_CONTENT_THRESHOLD) {
                        log(`检测到超长内容 (${contentLength}字符)，使用简化处理`);
                        // 对超长内容使用更安全的插入方式
                        insertSimplifiedQuote(virtualQuote);
                    } else {
                        // 正常处理
                        virtualQuote.insertIntoPost();
                    }

                    // 标记处理完成
                    virtualQuote.post.setAttribute('data-quote-processed', 'true');

                    // 隐藏reply-tab
                    const replyTab = virtualQuote.post.querySelector('.reply-to-tab');
                    if (replyTab) replyTab.style.display = 'none';

                    successCount++;

                } catch (error) {
                    errorCount++;
                    log(`批量DOM操作错误 (${index}/${batch.length}):`, error);

                    // 尝试降级处理
                    try {
                        insertFallbackQuote(virtualQuote);
                        virtualQuote.post?.setAttribute('data-quote-processed', 'true');
                        log(`降级处理成功: ${index}`);
                    } catch (fallbackError) {
                        log(`降级处理也失败: ${index}`, fallbackError);
                        // 最后的保险：至少隐藏reply-tab
                        try {
                            const replyTab = virtualQuote.post?.querySelector('.reply-to-tab');
                            if (replyTab) replyTab.style.display = 'none';
                        } catch (e) {
                            // 忽略最终错误
                        }
                    }
                }
            });

            if (successCount > 0 || errorCount > 0) {
                log(`批处理完成: 成功${successCount}个，失败${errorCount}个`);
            }

            // 如果还有待处理的，继续下一批
            if (domOperationQueue.length > 0) {
                setTimeout(processDOMBatch, errorCount > 0 ? 10 : 1); // 如果有错误，稍微延长间隔
            }
        });
    }

    // 简化的引用插入（用于超长内容）
    function insertSimplifiedQuote(virtualQuote) {
        try {
            const regularContents = virtualQuote.post.querySelector('.regular.contents');
            if (!regularContents || regularContents.hasAttribute('data-quote-added')) return false;

            regularContents.setAttribute('data-quote-added', 'true');

            const postContent = regularContents.querySelector('.cooked') || regularContents;
            if (!postContent || !postContent.isConnected) return false;

            // 创建简化的引用元素
            const simpleQuote = document.createElement('div');
            simpleQuote.className = 'optimized-reply-quote';
            simpleQuote.style.cssText = 'margin-bottom: 10px; padding: 8px 12px; background: rgba(0, 0, 0, 0.05); border-radius: 10px; font-size: 14px;';

            const header = document.createElement('div');
            header.className = 'quote-header';
            header.style.cssText = 'font-weight: bold; margin: 5px; color: #555;';
            // 确保作者名是清理过的
            const cleanAuthor = virtualQuote.cleanAuthorName ? virtualQuote.cleanAuthorName(virtualQuote.author) : virtualQuote.author;
            header.textContent = cleanAuthor;

            const content = document.createElement('div');
            content.className = 'quote-content';
            content.style.cssText = 'color: #666; line-height: 1.4; word-wrap: break-word; padding: 0 0 0 4px;';

            // 安全地设置内容（纯文本）
            let textContent = '';
            if (virtualQuote.content instanceof Element) {
                textContent = virtualQuote.content.textContent || '';
            } else if (typeof virtualQuote.content === 'string') {
                textContent = virtualQuote.content.replace(/<[^>]*>/g, '');
            }
            content.textContent = textContent.length > 800 ?
                textContent.substring(0, 800) + '... (长内容已简化显示)' :
                textContent;

            simpleQuote.appendChild(header);
            simpleQuote.appendChild(content);

            // 安全的DOM插入
            if (!safeInsertBefore(postContent, simpleQuote, postContent.firstChild)) {
                log('简化引用插入失败，尝试appendChild');
                try {
                    postContent.appendChild(simpleQuote);
                } catch (appendError) {
                    log('简化引用appendChild也失败:', appendError);
                    return false;
                }
            }

            return true;
        } catch (error) {
            log('insertSimplifiedQuote出错:', error);
            return false;
        }
    }

    // 降级引用插入（最后的保险）
    function insertFallbackQuote(virtualQuote) {
        try {
            const regularContents = virtualQuote.post.querySelector('.regular.contents');
            if (!regularContents || regularContents.hasAttribute('data-quote-added')) return false;

            regularContents.setAttribute('data-quote-added', 'true');

            const postContent = regularContents.querySelector('.cooked') || regularContents;
            if (!postContent || !postContent.isConnected) return false;

            // 创建最简单的引用提示
            const fallbackQuote = document.createElement('div');
            fallbackQuote.style.cssText = 'margin-bottom: 10px; padding: 8px 12px; background: rgba(0, 0, 0, 0.05); border-radius: 10px; font-size: 14px; color: #666;';
            // 确保作者名是清理过的
            const cleanAuthor = virtualQuote.cleanAuthorName ? virtualQuote.cleanAuthorName(virtualQuote.author) : virtualQuote.author;
            fallbackQuote.textContent = `引用自 ${cleanAuthor}: [内容过长无法显示]`;

            // 最安全的插入方式
            if (!safeInsertBefore(postContent, fallbackQuote, postContent.firstChild)) {
                log('最终降级引用插入失败，尝试appendChild');
                try {
                    postContent.appendChild(fallbackQuote);
                } catch (appendError) {
                    log('最终降级引用appendChild也失败:', appendError);
                    return false;
                }
            }

            return true;
        } catch (error) {
            log('insertFallbackQuote出错:', error);
            return false;
        }
    }

     // 超高速引用创建函数
     function createQuoteInstantly(post, { content, author, floorNumber, avatarSrc }) {
         const startTime = performance.now();
         try {
             // 创建虚拟引用对象
             const virtualQuote = new VirtualQuote(post, content, author, floorNumber, avatarSrc);

             // 添加到批量处理队列
             addToDOMQueue(virtualQuote);

             const endTime = performance.now();
             updatePerformanceStats(endTime - startTime);

             log(`引用创建排队: ${post.id} -> ${author} #${floorNumber} (${(endTime - startTime).toFixed(2)}ms)`);

         } catch (error) {
             log('创建引用时出错:', error);
         }
     }

     // 内存映射引用系统
     const postElementMap = new WeakMap();
     const quoteDataMap = new Map();

     // 建立帖子映射
     function buildPostMap() {
         const posts = document.querySelectorAll('article[id^="post_"]');

         posts.forEach(post => {
             const postId = getFloorNumber(post);
             if (postId) {
                 postElementMap.set(post, postId);

                 const content = post.querySelector('.cooked');
                 const author = post.querySelector('.names .username');
                 const avatar = post.querySelector('.topic-avatar img.avatar');

                 if (content && author) {
                     quoteDataMap.set(postId, {
                         content: content.cloneNode(true),
                         author: author.textContent.trim(),
                         floorNumber: postId,
                         avatarSrc: avatar?.getAttribute('src') || '',
                         element: post
                     });
                 }
             }
         });

         log(`建立帖子映射: ${quoteDataMap.size}个帖子`);
     }

     // 闪电般的引用解析
     function lightningQuoteExtraction(post) {
         const replyTab = post.querySelector('.reply-to-tab');
         const replyToPostId = replyTab?.getAttribute('aria-controls');

         if (!replyToPostId) return false;

         const targetId = replyToPostId.replace('top--', '');
         const quoteData = quoteDataMap.get(targetId);

         if (quoteData) {
             // 立即创建引用，无需等待
             createQuoteInstantly(post, quoteData);
             return true;
         }

         return false;
     }

     // 超级预加载系统 - 优化版
     function superPreloadQuotes() {
         if (document.body?.classList.contains('super-preload-completed')) return;

         log('启动超级预加载系统...');
         const startTime = performance.now();

         // 建立帖子映射
         buildPostMap();

         // 批量处理所有引用
         const allPosts = document.querySelectorAll('article[id^="post_"]');
         let processedCount = 0;
         let networkRequestCount = 0;

         // 分批处理，避免阻塞
         const processBatch = (posts, batchIndex = 0) => {
             const batchSize = 10;
             const start = batchIndex * batchSize;
             const end = Math.min(start + batchSize, posts.length);
             const batch = Array.from(posts).slice(start, end);

             batch.forEach(post => {
                 if (post.hasAttribute('data-quote-added')) return;

                 // 优先使用闪电引用解析
                 if (lightningQuoteExtraction(post)) {
                     post.setAttribute('data-quote-added', 'true');
                     processedCount++;
                 } else if (CONFIG.SMART_PRELOAD_ENABLED) {
                     // 对于视口附近的帖子，启动网络请求
                     if (isElementNearViewport(post, CONFIG.VIEWPORT_PRELOAD_DISTANCE)) {
                         const replyTab = post.querySelector('.reply-to-tab');
                         const replyToPostId = replyTab?.getAttribute('aria-controls');

                         if (replyToPostId && networkRequestCount < 5) { // 限制预加载数量
                             processPostOptimized(post);
                             networkRequestCount++;
                         }
                     }
                 }
             });

             // 继续处理下一批
             if (end < posts.length) {
                 setTimeout(() => processBatch(posts, batchIndex + 1), 10);
             } else {
                 // 预加载完成
                 document.body?.classList.add('super-preload-completed');
                 const endTime = performance.now();
                 log(`超级预加载完成: ${processedCount}个即时引用，${networkRequestCount}个网络请求，耗时${(endTime - startTime).toFixed(2)}ms`);
             }
         };

         processBatch(allPosts);
     }

     // 智能视口预加载
     function smartViewportPreload() {
         if (!CONFIG.SMART_PRELOAD_ENABLED) return;

         const posts = document.querySelectorAll('article[id^="post_"]:not([data-quote-added])');
         let preloadCount = 0;

         posts.forEach(post => {
             if (preloadCount >= 3) return; // 限制同时预加载数量

             if (isElementNearViewport(post, CONFIG.VIEWPORT_PRELOAD_DISTANCE)) {
                 const replyTab = post.querySelector('.reply-to-tab');
                 const replyToPostId = replyTab?.getAttribute('aria-controls');

                 if (replyToPostId) {
                     // 检查是否已在缓存中
                     const cachedPost = postCache.get(replyToPostId);
                     if (!cachedPost) {
                         processPostOptimized(post);
                         preloadCount++;
                         log(`智能预加载: ${replyToPostId}`);
                     }
                 }
             }
         });
     }

 })();