// ==UserScript==
// @name         linux.do美化
// @description  美化linux.do论坛布局，使用最少量的JavaScript
// <AUTHOR>
// @version      1.7
// @match        https://linux.do/*
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==
(function () {
  'use strict';

  // 在页面开始加载时就执行
  document.addEventListener('DOMContentLoaded', function() {
      console.log('linux.do美化脚本已加载，开始执行...');

      // 立即添加CSS类到body，以便尽早应用样式
      document.body.classList.add('linux-do-beautify');
      // 添加元数据容器并整理元数据元素
      function organizeTopicMetadata() {
          const topicItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
          console.log(`找到${topicItems.length}个未处理的topic-list-item`);

          topicItems.forEach(function(item) {
              // 双重检查是否已经处理过，避免重复处理
              if (item.querySelector('.topic-metadata-container') || item.dataset.metadataProcessed === 'true') {
                  console.log('跳过已处理的元素');
                  item.dataset.metadataProcessed = 'true'; // 确保标记为已处理
                  return;
              }

              const mainLink = item.querySelector('td.main-link');
              if (!mainLink) {
                  console.log('未找到main-link元素，跳过');
                  return;
              }

              // 再次检查mainLink中是否已有元数据容器
              if (mainLink.querySelector('.topic-metadata-container')) {
                  console.log('main-link中已有元数据容器，跳过');
                  item.dataset.metadataProcessed = 'true';
                  return;
              }

              // 使用DocumentFragment批量构建DOM结构
              const fragment = document.createDocumentFragment();

              // 创建元数据容器
              const metadataContainer = document.createElement('div');
              metadataContainer.className = 'topic-metadata-container';
              // 设置样式，但不添加到DOM，等所有子元素准备好后一次性添加
              metadataContainer.style.opacity = '1';
              metadataContainer.style.transition = 'opacity 0.2s ease';

              // 1. 添加作者名字
              let authorName = '';
              const userLink = item.querySelector('a[data-user-card]');
              if (userLink) {
                  authorName = userLink.getAttribute('data-user-card');
                  const authorElement = document.createElement('div');
                  authorElement.className = 'topic-author-name topic-metadata-item';
                  authorElement.textContent = authorName;
                  metadataContainer.appendChild(authorElement);
              }

              // 2. 移动浏览量到元数据容器
              const viewsElement = item.querySelector('td.num.views');
              if (viewsElement) {
                  const viewsMetadata = document.createElement('div');
                  viewsMetadata.className = 'topic-metadata-item views-metadata';
                  // 直接设置最终HTML内容，避免多次修改
                  viewsMetadata.innerHTML = '<span class="views-icon"></span>' + viewsElement.textContent;
                  metadataContainer.appendChild(viewsMetadata);
              }

              // 3. 移动热度指标到元数据容器
              const heatmapElement = item.querySelector('.topic-list-data.heatmap-low');
              if (heatmapElement) {
                  const heatmapMetadata = document.createElement('div');
                  heatmapMetadata.className = 'topic-metadata-item heatmap-metadata';
                  heatmapMetadata.innerHTML = '<span class="heatmap-icon"></span>' + heatmapElement.textContent;
                  metadataContainer.appendChild(heatmapMetadata);
              }

              // 4. 移动回复数到元数据容器
              const postsElement = item.querySelector('td.num.posts, td.num.posts-map.posts');
              if (postsElement) {
                  const postsMetadata = document.createElement('div');
                  postsMetadata.className = 'topic-metadata-item posts-metadata';
                  postsMetadata.innerHTML = '<span class="posts-icon"></span>' + postsElement.textContent;
                  metadataContainer.appendChild(postsMetadata);
              }

              // 5. 移动时间到元数据容器 - 优先使用创建时间(age)，如果没有则使用活动时间(activity)
              // 注意：只添加一个时间元素，避免重复
              const ageElement = item.querySelector('td.activity.num.topic-list-data.age');
              const activityElement = item.querySelector('td.num.activity');

              // 优先使用创建时间
              if (ageElement) {
                  const timeMetadata = document.createElement('div');
                  timeMetadata.className = 'topic-metadata-item age-metadata';
                  timeMetadata.innerHTML = '<span class="age-icon"></span>' + ageElement.textContent;
                  metadataContainer.appendChild(timeMetadata);
                  console.log('添加创建时间元素');
              }
              // 如果没有创建时间，则使用活动时间
              else if (activityElement) {
                  const timeMetadata = document.createElement('div');
                  timeMetadata.className = 'topic-metadata-item activity-metadata';
                  timeMetadata.innerHTML = '<span class="activity-icon"></span>' + activityElement.textContent;
                  metadataContainer.appendChild(timeMetadata);
                  console.log('添加活动时间元素');
              }

              // 将准备好的容器添加到fragment
              fragment.appendChild(metadataContainer);

              // 一次性将fragment添加到DOM
              mainLink.appendChild(fragment);

              // 标记该项已处理
              item.dataset.metadataProcessed = 'true';
          });
      }

      // 立即执行一次，尽早处理页面元素
      console.log('立即执行初始化操作...');
      organizeTopicMetadata();

      // 添加楼主标识的函数
      function addTopicOwnerClass() {
          // 检查当前是否在帖子详情页
          if (!window.location.pathname.startsWith('/t/')) {
              return;
          }

          console.log('添加楼主标识...');

          // 获取第一个帖子（通常是楼主帖）
          const firstPost = document.querySelector('#post_1');
          if (!firstPost) {
              console.log('未找到第一个帖子');
              return;
          }

          // 检查是否已有topic-owner类
          if (firstPost.classList.contains('topic-owner')) {
              console.log('第一个帖子已有topic-owner类');
              return;
          }

          // 获取第一个帖子的作者
          const firstPostAuthor = firstPost.querySelector('[data-user-card]');
          if (!firstPostAuthor) {
              console.log('未找到第一个帖子的作者信息');
              return;
          }

          const firstPostUsername = firstPostAuthor.getAttribute('data-user-card');

          // 获取主题作者（从主题元数据中获取）
          const topicAuthor = document.querySelector('.topic-meta-data [data-user-card]');
          if (!topicAuthor) {
              console.log('未找到主题作者信息');
              return;
          }

          const topicUsername = topicAuthor.getAttribute('data-user-card');

          // 比较作者是否一致，如果一致则添加topic-owner类
          if (firstPostUsername === topicUsername) {
              firstPost.classList.add('topic-owner');
              console.log(`为第一个帖子添加topic-owner类 (作者: ${firstPostUsername})`);
          } else {
              console.log(`第一个帖子作者 (${firstPostUsername}) 与主题作者 (${topicUsername}) 不一致`);
          }
      }

      // 立即尝试添加楼主标识
      addTopicOwnerClass();

      // 在DOM完全加载后执行一次，处理可能的动态内容
      setTimeout(function() {
          console.log('DOM加载后执行...');

          // 检查是否有新的未处理的topic-list-item
          const unprocessedItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
          if (unprocessedItems.length > 0) {
              console.log('发现未处理的列表项，进行处理...');
              organizeTopicMetadata();
          }

          // 再次尝试添加楼主标识（以防DOM还未完全加载）
          addTopicOwnerClass();
      }, 300);

      // 添加防抖动函数，带有执行锁定机制
      function debounce(func, wait) {
          let timeout;
          let isExecuting = false; // 添加执行锁定标志

          return function() {
              // 如果正在执行，直接返回，避免重复执行
              if (isExecuting) return;

              const context = this;
              const args = arguments;

              // 清除之前的定时器
              clearTimeout(timeout);

              timeout = setTimeout(() => {
                  // 设置执行锁定
                  isExecuting = true;

                  try {
                      // 执行函数
                      func.apply(context, args);
                  } finally {
                      // 无论执行成功还是失败，都解除锁定
                      setTimeout(() => {
                          isExecuting = false;
                      }, 50); // 短暂延迟解锁，防止极端情况下的重复触发
                  }
              }, wait);
          };
      }

      // 防抖动处理函数 - 使用更严格的检查
      const debouncedOrganizeMetadata = debounce(function() {
          console.log('执行防抖动的元数据组织...');

          // 再次检查是否有未处理的元素
          const unprocessedItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
          if (unprocessedItems.length > 0) {
              console.log(`发现${unprocessedItems.length}个未处理的列表项，进行处理...`);

              // 确保不会重复处理
              const itemsToProcess = Array.from(unprocessedItems).filter(item =>
                  !item.querySelector('.topic-metadata-container'));

              if (itemsToProcess.length > 0) {
                  console.log(`实际需要处理的列表项：${itemsToProcess.length}个`);
                  organizeTopicMetadata();
              } else {
                  console.log('所有元素已有元数据容器，仅更新处理标记');
                  unprocessedItems.forEach(item => item.dataset.metadataProcessed = 'true');
              }
          }

          // 在DOM变化时也尝试添加楼主标识
          addTopicOwnerClass();
      }, 150); // 增加延迟时间，给DOM更多时间稳定

      // 监听DOM变化，处理动态加载的内容
      const observer = new MutationObserver(function(mutations) {
          // 检查是否有未处理的元素，而不是仅检查是否有新元素
          // 这样可以避免重复处理已有元素
          const checkUnprocessedItems = () => {
              const unprocessedItems = document.querySelectorAll('.topic-list-item:not([data-metadata-processed="true"])');
              return unprocessedItems.length > 0;
          };

          // 使用更精确的检查，减少不必要的处理
          let shouldCheckMetadata = false;

          // 快速检查是否有相关变化
          for (const mutation of mutations) {
              if (mutation.type !== 'childList' || mutation.addedNodes.length === 0) continue;

              // 检查是否添加了新的DOM元素
              const hasNewElements = Array.from(mutation.addedNodes).some(node =>
                  node.nodeType === Node.ELEMENT_NODE);

              if (hasNewElements) {
                  shouldCheckMetadata = true;
                  break;
              }
          }

          // 只有在确实需要检查时才执行检查
          let hasUnprocessedItems = false;

          if (shouldCheckMetadata) {
              hasUnprocessedItems = checkUnprocessedItems();
          }

          // 使用防抖动函数处理变化
          if (hasUnprocessedItems) {
              console.log('MutationObserver检测到未处理的列表项');
              debouncedOrganizeMetadata();
          }
      });

      // 配置观察器 - 更精确地定义观察目标
      observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: false, // 不监听属性变化
          characterData: false // 不监听文本变化
      });
  });

  GM_addStyle(`
/* 禁用头像滚动跟随功能 */
.topic-post.sticky-avatar .topic-avatar {
position: relative !important;
top: unset !important;
}

body::before, body::after {
  content: "";
  display: none !important;
}


`);
})();