<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavDB 图片预览测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .preview-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .preview-item img {
            width: 200px;
            height: auto;
            display: block;
            cursor: pointer;
        }
        .preview-item p {
            padding: 10px;
            margin: 0;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>JavDB 图片预览功能测试</h1>
    <p>鼠标悬停在下面的图片上应该会显示大图预览（宽度600px，位置在正上方或正下方）</p>
    
    <div class="preview-container">
        <!-- 模拟JavDB的图片结构 -->
        <div class="preview-item">
            <img src="https://c0.jdbstatic.com/samples/yw/YwqAZb_s_0.jpg" alt="预览图1">
            <p>测试图片 1</p>
        </div>
        
        <div class="preview-item">
            <img src="https://c0.jdbstatic.com/samples/yw/YwqAZb_s_1.jpg" alt="预览图2">
            <p>测试图片 2</p>
        </div>
        
        <div class="preview-item">
            <img src="https://c0.jdbstatic.com/samples/yw/YwqAZb_s_2.jpg" alt="预览图3">
            <p>测试图片 3</p>
        </div>
        
        <div class="preview-item">
            <img src="https://c0.jdbstatic.com/samples/yw/YwqAZb_s_3.jpg" alt="预览图4">
            <p>测试图片 4</p>
        </div>
    </div>

    <script>
        // 模拟用户脚本环境
        function GM_addStyle(css) {
            const style = document.createElement('style');
            style.textContent = css;
            document.head.appendChild(style);
        }
        
        function GM_getValue(key, defaultValue) {
            return localStorage.getItem(key) || defaultValue;
        }
        
        function GM_setValue(key, value) {
            localStorage.setItem(key, value);
        }
        
        // 这里会插入JavDB美化脚本的图片预览部分
        console.log('测试页面加载完成');
        
        // 简化版的图片预览功能用于测试
        function initTestImagePreview() {
            // 创建预览窗口
            const previewContainer = document.createElement('div');
            previewContainer.id = 'javdb-image-preview';
            previewContainer.style.cssText = `
                position: fixed;
                z-index: 10000;
                background: rgba(0, 0, 0, 0.9);
                border-radius: 8px;
                padding: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
                max-width: 600px;
                width: 600px;
                height: auto;
                display: none;
            `;

            const previewImg = document.createElement('img');
            previewImg.style.cssText = `
                width: 100%;
                height: auto;
                border-radius: 4px;
                display: block;
            `;

            previewContainer.appendChild(previewImg);
            document.body.appendChild(previewContainer);

            // 查找所有预览图片
            const previewImages = document.querySelectorAll('img[src*="_s_"]');
            console.log('找到预览图片:', previewImages.length);
            
            previewImages.forEach(img => {
                img.addEventListener('mouseenter', function(e) {
                    const smallImageUrl = e.target.src;
                    const largeImageUrl = smallImageUrl.replace('_s_', '_l_');
                    
                    console.log('显示预览:', smallImageUrl, '->', largeImageUrl);
                    
                    previewImg.src = largeImageUrl;
                    
                    const rect = e.target.getBoundingClientRect();
                    const viewportWidth = window.innerWidth;
                    
                    let left = rect.left + (rect.width / 2) - 300;
                    if (left < 10) left = 10;
                    if (left + 600 > viewportWidth - 10) left = viewportWidth - 610;
                    
                    let top = rect.top > 350 ? rect.top - 350 : rect.bottom + 10;
                    
                    previewContainer.style.left = left + 'px';
                    previewContainer.style.top = top + 'px';
                    previewContainer.style.display = 'block';
                    previewContainer.style.opacity = '1';
                });
                
                img.addEventListener('mouseleave', function() {
                    previewContainer.style.opacity = '0';
                    setTimeout(() => {
                        if (previewContainer.style.opacity === '0') {
                            previewContainer.style.display = 'none';
                        }
                    }, 300);
                });
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initTestImagePreview, 500);
        });
    </script>
</body>
</html>
