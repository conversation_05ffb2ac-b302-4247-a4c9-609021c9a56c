// ==UserScript==
// @name         贴吧美化
// @description  精简美化页面，去除广告内容，在主页帖子列表可以自定义屏蔽用户的帖子，页面平滑显示防止闪烁
// @version      1.1
// <AUTHOR>
// @match        http*://tieba.baidu.com/*
// @run-at       document-start
// @grant        GM_addStyle
// ==/UserScript==

(function () {
	"use strict";

	// 使用样式注入CSS，避免直接操作DOM
	if (typeof GM_addStyle === "function") {
		GM_addStyle(`
body { visibility: hidden !important; opacity: 0 !important; transition: opacity 0.5s ease !important; }
body.script-ready { visibility: visible !important; opacity: 1 !important; }
		`);
	} else {
		// 备选方案：创建style元素
		const styleEl = document.createElement("style");
		styleEl.textContent = `
body { visibility: hidden !important; opacity: 0 !important; transition: opacity 0.5s ease !important; }
body.script-ready { visibility: visible !important; opacity: 1 !important; }
		`;
		document.head ? document.head.appendChild(styleEl) :
			document.addEventListener("DOMContentLoaded", () => document.head.appendChild(styleEl));
	}

	// 自定义样式定义
	const customStyles = `
* {
  border-radius: 8px;
  font-family: "PingFang SC", "Hiragino Sans", "Microsoft YaHei","SF Pro", Arial, sans-serif !important;
  font-weight: 500 !important;
  text-decoration: none !important;

}
.wrap2,
.content,
html, body {
    background: #E0E4E6 !important;
    color: #333 !important;
}
.forum_info .forum_info_desc,
.my_current_forum .exp_lable,
.threadlist_bright .threadlist_abs_onlyline,
.threadlist_bright .threadlist_abs,
.threadlist_bright .threadlist_author a:visited,
.threadlist_bright .threadlist_author a {
    color: #333 !important;
}
.threadlist_bright .threadlist_abs_onlyline, .threadlist_bright .threadlist_abs {
    color: #000;
    font-size: 13px;
}
.threadlist_title a:visited,
a {
    color: #0A8581;
}
.threadlist_title {
    font-size: 14px;
    overflow: visible !important;
    text-overflow: ellipsis;
    white-space: normal !important;
}

/*屏蔽 */
.threadlist_bright li.thread_top_list_folder, .threadlist_bright li.thread_top_list_folder:hover,
#selectsearch-icon,
.l_reply_num,
.d_badge_title,
.loading_reply,
.header,
.footer,
.pb-title-answer-icon,
.ui_card_wrap .arrow,
.card_userinfo_guide,
.nicknameEmoji,
.is_show_create_time,
.tb_icon_author_rely.j_replyer,
.my_current_forum .rank,
.region_header.clearfix,
.user_score,
.search_internal_btn,
.card_banner,
#com_userbar,  /* 用户栏 */
.u_split,
.poster_head_surveillance,j_surveillance,
.arrow_top,
.u_member,
.u_bdhome,
.u_creative,
.u_agent,
.u_setting,
.u_official,
.save_face_bg.save_face_bg_2,
.share_btn_wrapper,
.my_current_forum .title,
.d_author .d_pb_icons,
.save_face_bg_1,
.thread_theme_5,
.lzl_link_fold,
.louzhubiaoshi,
.user-hide-post-down,
.nav_list.j_nav_list,
.card_top_right,
.card_banner img,
.region_bright.app_download_box,
.right_section.right_bright,
.aside_region.celebrity,
.threadlist_bright .frs_bright_icons,
.head_inner,
#celebrity,
#thread_top_folder,
body > ul {
  display: none !important;
}
.icon_turnleft,
.icon_ypic,
.icon_retract,
.icon_turnright {
  opacity: 0;
}

/* ---------------------头图-----------------------*/
.head_main .head_middle, .head_main .head_content {
    width: 982px;
    margin: 40px auto;
}
.card_top_theme {
    height: 200px;
    border-top: 1px solid #E9EBF0;
    background: #0A8581;
    border-radius: 10px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.25);
}
.card_slogan,
.card_numLabel,
.card_info .bottom_list span {
    color: #fff;
}
.card_menNum, .card_infoNum {
    color: #fff;
}
.card_title_fname {
    font-size: 30px;
    color: #fff !important;
    margin-right: 15px;
}
.card_top_theme .card_head, .card_top_theme .card_head_img {
    background: #FFF;
    border-radius: 20px;
    border: none !important;
    height: 100px;
    width: 100px;

}
.card_top_theme .card_head {
    box-shadow: 0 1px 14px rgba(0, 0, 0, 0.5);
    outline: 4px solid #000;
    margin: 20px 0 20px 40px;
}
.card_top_theme .card_top {
    padding-left: 180px;
    padding-top: 120px;
}
.islike_focus,
.cancel_focus {
    background: none;
}
.islike_focus, .cancel_focus {
    width: 70px;
    height: 26px;
    font-size: 14px;
    line-height: 26px !important;
    border-radius: 18px;
    background-color: #00000066;
    color: white !important;
    text-align: center;
    position: relative;
    display: inline-block;
    float: left;
    margin: 7px 10px 0 0px !important;
    letter-spacing: 1px;
}
.islike_focus::before {
    content: "关注";
}
.cancel_focus::before {
    content: "已关注";
}
.card_top_theme .card_title {
    margin: 0;
    display: flex;
    align-items: center;
}
/*----搜索----*/
.search_internal_wrap {
    height: 25px;
    margin: -54px 32px 0 0;
}
.search_internal_input {
    width: 146px;
    height: 30px;
    padding: 0 10px;
    color: #fff;
    border: none;
    background: #0a504d;
}
.search_internal_input::placeholder {
    color: #318480;
}
/* --------------------主页--------------------------*/
.forum_content {
    background: none;
    border: none;
}
.nav_wrap {
    height: 0px;
    border: none;
}
/*------------右侧----------*/
.forum_content .aside {
    float: right;
    width: 220px;
    display: inline;
    background: #fff;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
}
.aside_region {
    border-bottom: 1px solid #e4e6eb;
    border-radius: 0;
}
.my_tieba .media_left, .my_tieba .media-left {
    padding: 0;
    border: none;
    outline: 3px solid #141414;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.25);
}
.my_current_forum .title {
    color: #0a8581;
}
.my_current_forum .rank_top {
    background:none;
    padding-left: 0px;
}
.my_current_forum .badge {
    width: 96px;
    border: none;
    background: #0a8581;
    margin-top: -37px;
}
.my_current_forum .badge_name {
    position: absolute;
    width: 60px;
    color: #fff;
    background: transparent;
    text-align: center;
}
.my_current_forum .exp_bar {
    border: none;
}
.my_current_forum .exp_bar_current {
    background: none;
    margin: 0;
    border-radius: 4px;
    background-color: #52C51A;
    border: 1px solid #52c51a;
}
.my_current_forum .exp_num {
    line-height: 13px !important;
}
.my_current_forum .exp_bar span,
.my_current_forum .exp_bar .exp_current_num {
    color: #fff;
}
.aside_media_horizontal a, .aside-media-horizontal a {
    color: #222 !important;
}
.media_disp .media_pic_control a {
    color: #fff !important;
}
/*-------------------------主列表--------------------------*/
.thread_item_box {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
    margin-bottom: 20px;
    background: #fff;
    min-height: 100px;
}
.threadlist_bright>li {
    border-bottom: 0;
}
.threadlist_rep_num {
    width: 35px;
    height: 20px;
    line-height: 21px;
    margin-right: 20px;
    color: #fff;
    background: #222;
    border-radius: 25px;
    /* box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1); */
}
.threadlist_bright>li:hover {
    background-color: #fff;
}
.threadlist_bright .threadlist_media li {
    margin: 10px;
    height: 135px;
    outline: 2px solid #000;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.25);
    display: flex;
    border-radius: 4px;
}
.thumbnail img {
    border-radius: 4px;
}
.threadlist_bright .media_disp {
    width: 540px;
    padding: 10px;
    background: #333;
    border: 0px solid #e4e6eb;
    margin-left: 90px;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.3);
}
.media_pic_control .line {
    color: transparent;
}
.media_bigpic img {
    margin: 5px auto;
}
.threadlist_bright .small_list {
    position: relative;
    width: 460px;
    overflow: hidden;
    background: #f5f5f7;
    outline: 1px solid #e8e8ed;
}
.my_current_forum .badge_index {
    background: #0a504d !important;
    border-radius: 0 6px 6px 0;
    color: #fff;

}
a:hover, a:focus,
.threadlist_title a:hover {
    color: #ff523f;
}
/*--------------*/
.threadlist_author {
    width: 150px;
    color: #333;
}
.threadlist_author .frs-author-name {
    display: inline-block;
    overflow: visible;
    max-width: 300px !important;
    width: 200px !important;
    white-space: nowrap;
    text-overflow: clip !important;
}
.threadlist_author .frs-author-name-wrap {
    display: inline-block;
    width: 90px;
}
.threadlist_bright .icon_author {
    width: 12px;                /* 设置图标的宽度 */
    height: 12px;               /* 设置图标的高度 */
    margin-right: 4px;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAACjdpQ0NQc1JHQiBJRUM2MTk2Ni0yLjEAAEiJnZZ3VFPZFofPvTe9UJIQipTQa2hSAkgNvUiRLioxCRBKwJAAIjZEVHBEUZGmCDIo4ICjQ5GxIoqFAVGx6wQZRNRxcBQblklkrRnfvHnvzZvfH/d+a5+9z91n733WugCQ/IMFwkxYCYAMoVgU4efFiI2LZ2AHAQzwAANsAOBws7NCFvhGApkCfNiMbJkT+Be9ug4g+fsq0z+MwQD/n5S5WSIxAFCYjOfy+NlcGRfJOD1XnCW3T8mYtjRNzjBKziJZgjJWk3PyLFt89pllDznzMoQ8GctzzuJl8OTcJ+ONORK+jJFgGRfnCPi5Mr4mY4N0SYZAxm/ksRl8TjYAKJLcLuZzU2RsLWOSKDKCLeN5AOBIyV/w0i9YzM8Tyw/FzsxaLhIkp4gZJlxTho2TE4vhz89N54vFzDAON40j4jHYmRlZHOFyAGbP/FkUeW0ZsiI72Dg5ODBtLW2+KNR/Xfybkvd2ll6Ef+4ZRB/4w/ZXfpkNALCmZbXZ+odtaRUAXesBULv9h81gLwCKsr51Dn1xHrp8XlLE4ixnK6vc3FxLAZ9rKS/o7/qfDn9DX3zPUr7d7+VhePOTOJJ0MUNeN25meqZExMjO4nD5DOafh/gfB/51HhYR/CS+iC+URUTLpkwgTJa1W8gTiAWZQoZA+J+a+A/D/qTZuZaJ2vgR0JZYAqUhGkB+HgAoKhEgCXtkK9DvfQvGRwP5zYvRmZid+8+C/n1XuEz+yBYkf45jR0QyuBJRzuya/FoCNCAARUAD6kAb6AMTwAS2wBG4AA/gAwJBKIgEcWAx4IIUkAFEIBcUgLWgGJSCrWAnqAZ1oBE0gzZwGHSBY+A0OAcugctgBNwBUjAOnoAp8ArMQBCEhcgQFVKHdCBDyByyhViQG+QDBUMRUByUCCVDQkgCFUDroFKoHKqG6qFm6FvoKHQaugANQ7egUWgS+hV6ByMwCabBWrARbAWzYE84CI6EF8HJ8DI4Hy6Ct8CVcAN8EO6ET8OX4BFYCj+BpxGAEBE6ooswERbCRkKReCQJESGrkBKkAmlA2pAepB+5ikiRp8hbFAZFRTFQTJQLyh8VheKilqFWoTajqlEHUJ2oPtRV1ChqCvURTUZros3RzugAdCw6GZ2LLkZXoJvQHeiz6BH0OPoVBoOhY4wxjhh/TBwmFbMCsxmzG9OOOYUZxoxhprFYrDrWHOuKDcVysGJsMbYKexB7EnsFO459gyPidHC2OF9cPE6IK8RV4FpwJ3BXcBO4GbwS3hDvjA/F8/DL8WX4RnwPfgg/jp8hKBOMCa6ESEIqYS2hktBGOEu4S3hBJBL1iE7EcKKAuIZYSTxEPE8cJb4lUUhmJDYpgSQhbSHtJ50i3SK9IJPJRmQPcjxZTN5CbiafId8nv1GgKlgqBCjwFFYr1Ch0KlxReKaIVzRU9FRcrJivWKF4RHFI8akSXslIia3EUVqlVKN0VOmG0rQyVdlGOVQ5Q3mzcovyBeVHFCzFiOJD4VGKKPsoZyhjVISqT2VTudR11EbqWeo4DUMzpgXQUmmltG9og7QpFYqKnUq0Sp5KjcpxFSkdoRvRA+jp9DL6Yfp1+jtVLVVPVb7qJtU21Suqr9XmqHmo8dVK1NrVRtTeqTPUfdTT1Lepd6nf00BpmGmEa+Rq7NE4q/F0Dm2OyxzunJI5h+fc1oQ1zTQjNFdo7tMc0JzW0tby08rSqtI6o/VUm67toZ2qvUP7hPakDlXHTUegs0PnpM5jhgrDk5HOqGT0MaZ0NXX9dSW69bqDujN6xnpReoV67Xr39An6LP0k/R36vfpTBjoGIQYFBq0Gtw3xhizDFMNdhv2Gr42MjWKMNhh1GT0yVjMOMM43bjW+a0I2cTdZZtJgcs0UY8oyTTPdbXrZDDazN0sxqzEbMofNHcwF5rvNhy3QFk4WQosGixtMEtOTmcNsZY5a0i2DLQstuyyfWRlYxVtts+q3+mhtb51u3Wh9x4ZiE2hTaNNj86utmS3Xtsb22lzyXN+5q+d2z31uZ27Ht9tjd9Oeah9iv8G+1/6Dg6ODyKHNYdLRwDHRsdbxBovGCmNtZp13Qjt5Oa12Oub01tnBWex82PkXF6ZLmkuLy6N5xvP48xrnjbnquXJc612lbgy3RLe9blJ3XXeOe4P7Aw99D55Hk8eEp6lnqudBz2de1l4irw6v12xn9kr2KW/E28+7xHvQh+IT5VPtc99XzzfZt9V3ys/eb4XfKX+0f5D/Nv8bAVoB3IDmgKlAx8CVgX1BpKAFQdVBD4LNgkXBPSFwSGDI9pC78w3nC+d3hYLQgNDtoffCjMOWhX0fjgkPC68JfxhhE1EQ0b+AumDJgpYFryK9Issi70SZREmieqMVoxOim6Nfx3jHlMdIY61iV8ZeitOIE8R1x2Pjo+Ob4qcX+izcuXA8wT6hOOH6IuNFeYsuLNZYnL74+BLFJZwlRxLRiTGJLYnvOaGcBs700oCltUunuGzuLu4TngdvB2+S78ov508kuSaVJz1Kdk3enjyZ4p5SkfJUwBZUC56n+qfWpb5OC03bn/YpPSa9PQOXkZhxVEgRpgn7MrUz8zKHs8yzirOky5yX7Vw2JQoSNWVD2Yuyu8U02c/UgMREsl4ymuOWU5PzJjc690iecp4wb2C52fJNyyfyffO/XoFawV3RW6BbsLZgdKXnyvpV0Kqlq3pX668uWj2+xm/NgbWEtWlrfyi0LiwvfLkuZl1PkVbRmqKx9X7rW4sVikXFNza4bKjbiNoo2Di4ae6mqk0fS3glF0utSytK32/mbr74lc1XlV992pK0ZbDMoWzPVsxW4dbr29y3HShXLs8vH9sesr1zB2NHyY6XO5fsvFBhV1G3i7BLsktaGVzZXWVQtbXqfXVK9UiNV017rWbtptrXu3m7r+zx2NNWp1VXWvdur2DvzXq/+s4Go4aKfZh9OfseNkY39n/N+rq5SaOptOnDfuF+6YGIA33Njs3NLZotZa1wq6R18mDCwcvfeH/T3cZsq2+nt5ceAockhx5/m/jt9cNBh3uPsI60fWf4XW0HtaOkE+pc3jnVldIl7Y7rHj4aeLS3x6Wn43vL7/cf0z1Wc1zleNkJwomiE59O5p+cPpV16unp5NNjvUt675yJPXOtL7xv8GzQ2fPnfM+d6ffsP3ne9fyxC84Xjl5kXey65HCpc8B+oOMH+x86Bh0GO4cch7ovO13uGZ43fOKK+5XTV72vnrsWcO3SyPyR4etR12/eSLghvcm7+ehW+q3nt3Nuz9xZcxd9t+Se0r2K+5r3G340/bFd6iA9Puo9OvBgwYM7Y9yxJz9l//R+vOgh+WHFhM5E8yPbR8cmfScvP174ePxJ1pOZp8U/K/9c+8zk2Xe/ePwyMBU7Nf5c9PzTr5tfqL/Y/9LuZe902PT9VxmvZl6XvFF/c+At623/u5h3EzO577HvKz+Yfuj5GPTx7qeMT59+A/eE8/vH0Tt4AAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAJcEhZcwAALiMAAC4jAXilP3YAAAAbdEVYdFNvZnR3YXJlAENlbHN5cyBTdHVkaW8gVG9vbMGn4XwAAAUaSURBVHic7dtZiFZlGAfw31iprVa0YNKCRkUb1cVpw/aFrGiFEopCW7HlJtrooqCgpItsLwuioo0WoyKE9rLoiRZpJdQ2ylSULLOZcfTr4j2jWPN9883Mez6dmv/NXJxz/t97/uddnuf5P9NWq9X8n9E2JMCQAK0VoCiKYRiJzcq/0I5laI+IVa0cT0sEKIqiDdugwJHYF6OxSXnLn/gVn+FNfITFEVH54CoXoCiKrXAGJkkvvmmD22uSGJ/hIcyIiKVVjq8yAcqvvjduwgnWTPdmsRwv40Z8U9VsqESA8uUPxt3YD239pKpJy2EKPq5ChKoE2BuPYv8MdDUEzo+IbzLwrYXsApRr/mGcqv9f/p9YhWdwSe49IasA5dSfhLuwcTbihOW4BI/nXAq5BdgWM3BINtK18S5Oi4jFuQhzCzBBmqqNjrqBYBnOjIiZuQizCVBGeLfhqiyEPaOGqbgu1zLIKcAm0vQ/NgthfczE6RGxPAdZTgG2w2vYJwthfXyOYyJiYQ6ynALsJMXxY7MQ1sdcHBURP+YgG4wCzMOR66MA2+N17JWFsD6+xNERsSAHWU4BNsNLOCILYX28hZMjYlkOspwCbIB7cHEWwvp4AFMiYmUOstyB0HmYjo2yka6NFbggIh7NRZhbgN2lc3rnbKRr43scHxHf5iLMLcBw3CslRLkywW7UpCxzSkR05iKtIh0ej+ewbVZiFuKMiHgvJ2kVAoyQ4vXLMCwT7UqpunRNRHRk4kR1FaGxeAIHZqL8EBMj4rtMfKtRZU3wcGnNDjQynIfJeHvQ1ARZnR5PwB0Y10+aubgSr1ZlmFTqC5QiHIpbpCrxhk0+2oUPcD3er9ItaoUx0oYx0jQ+B7uoL0SXdNY/Lhkjv1TtDrXMGyxD5bE4RrLHxmFUeXmpNN3flGoK83KFur1hnbjDZcC0uTXe4HL8kTPAaRZD9viQAK3bA9qkzW+EZJSOsGYz7EKH1CfQga5WWONUHwdsIWWGe0hO8a5SX8CWknPUnTavwF/4DfMxB1/ga/yI3wdFHFB+5VGSI3wcDpNeemt9rxGswBJJjHekNHs2lq531lj54jvgJJyFA6SvnyslruF3fIKnpb6BLDHCgAUo/cAzcaFUEB0+0EH1gk6pMDodz0bEooGQ9VuAoig2kqb4tRgvbWqtRIdklt6KdyJiRX9I+iVAURRb4lIpUdm+Pz+cEQswDfdFxG99fbjPAhRFMQY342x97/upCu14CjdExM99ebBPAhRFsbOk9knYoC8/1AKslDbHKyPih2YfalqAoihGS2WpU6x/L9+NVXhRKpzOb+aBpgQoXZ+p0k7fbE6/rtAlnRBXN+Me9SpAGdFdhNtV1/mRG39KjRrTe0urmxFgXzyv/2WtdYW5Uhl9dqObGgpQFMVIqeNrsvxGR9XoNlIuj4j2ejf1JsCheEF+k6NVWCR1lc2qd0NdAcpIb5rUmzfYvn43arhfOhp7jBQbCTBWysB2rWx4rcEcyVCd19PFRgJMxCOqT26qRqfUZ/xkTxd7FKCs4N4lxfv/BdwnbYb/OhLrCTAKr0imxn8Bs3BiT43W9QTYEW8Y/Ou/G3Ok1rqf/nmhngCjJYNiz+rH1hJ8JTVX/is/qCfACDyIcw3eI7AbNTyGi3rqLWh0Chwo/dfHbpUOr3p8i3MjInq62EiAYTgIV0hV3laXvAaKDnyKO/FhvbJ6M8nQSKmsPdjigU4saZQHMGSNDQnwvxfgbwlcTZ/8CRdvAAAAAElFTkSuQmCC');
    background-size: contain;  /* 使背景图适应容器大小 */
    background-repeat: no-repeat; /* 防止背景图重复 */
}
.threadlist_bright .threadlist_author a:hover, .threadlist_bright .threadlist_author a:focus {
    color: #ff523f;
}

.threadlist_reply_date.pull_right.j_reply_data {
    margin-top: -25px;
}
/*-----------pop------------*/
.ui_card_wrap {
    background: none;
}
.ui_card_content {
    border: none;
    box-shadow: 0 1px 20px rgba(0, 0, 0, 0.2);
}
.card_userinfo_wrap {
    padding-bottom: 6px;
    width: 368px;
    background: none;
    /* border: none; */
}

/*-----------看帖页面-------------*/
.core_title_wrap_bright {
    width: 980px;
    background: #ffffffcc;
    border: none;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.25);
    margin-bottom: 15px;
    backdrop-filter: blur(50px) !important;
}
.card_head {
    border-radius: 12px;
    box-shadow: 0 1px 14px rgba(0, 0, 0, 0.5);
    outline: 2px solid #000;
}
.card_top_theme2 {
    border: none ;
    margin-right: -2px;
    background: none;
    background: #0A8581;
    margin: 30px 0 15px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.25);
}
.core_title_btns .j_quick_reply .icon-reply {
    background: none !important;
    margin-right: -16px;
}
.pb_content {
    width: 980px;
    background: none;
    border-right: none;
    background: none;
}
.left_section {
    width: 980px;
    float: left;
    background: none;
}
.l_post_bright {
    border: none;
    background: #f7f8fa;
    width: 981px;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.25);
    margin-bottom: 15px;
}
.d_post_content_main {
    position: relative;
    float: left;
    width: 784px;
    padding: 15px;
    background: #fff;
    margin-left: 35px;
    border-left: 1px solid #e3e1eb;
    border-radius: 0 8px 8px 0;
}
.core_reply_wrapper {
    background: #f7f8fa;
    width: 780px;
    border: 1px solid #f0f1f2;
    margin-top: 4px;
    min-height: 0;
}
.d_author, .d_author_anonym {
    width: 96px;
    float: left;
    text-align: center;
    padding: 15px 0 0 35px;
    display: block;
}
.lzl_editor_container div.edui-editor-body {
    border: 1px solid #d9d9d9;
    height: 30px !important;
    color: #666;
}
.lzl_panel_submit {
    color: #FFF;
    border-radius: 6px;
    background: #0a8581;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.10);
}
.lzl_editor_container_s {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}
.lzl_jb_in {
    padding-right: 10px;
    width: auto;
    height: 0px;
    background: none;
    display: inline-block;
    position: relative;
}
.lzl_jb_in::before {
    content: "举报";
    font-size: 12px;
    color: #999;
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
/* 隐藏原始图片 */
.j_jb_ele .icon-jubao {
    display: none; /* 隐藏图片 */
}
/* 在 <a> 标签上添加伪元素 */
.j_jb_ele .tail-info {
    position: relative; /* 为伪元素定位做准备 */
    display: inline-block; /* 确保 <a> 是行内块元素 */
    width: 40px; /* 设置宽度 */
    height: 13px; /* 设置高度 */
    font-size: 12px; /* 设置字体大小 */
    line-height: 13px; /* 与高度一致，确保文字垂直居中 */
    text-align: center; /* 文字水平居中 */
    color: #999; /* 设置文字颜色 */
    margin-left: 7px; /* 保持原有样式 */
    vertical-align: middle; /* 保持原有样式 */
    margin-top: -0.2em; /* 保持原有样式 */
}
/* 使用伪元素添加文字"举报" */
.j_jb_ele .tail-info::after {
    content: "举报"; /* 设置伪元素内容 */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/*头像*/
.p_author_face {
    background: none repeat scroll 0 0 #FFF;
    display: block;
    height: 80px !important;
    padding: 0px !important;
    width: 80px !important;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 14px rgba(0, 0, 0, 0.7);
    outline: 3px solid #000 !important;
}
.d_badge_bright,
.d_badge_bright .d_badge_lv,
.d_badge_icon1 .d_badge_lv, .d_badge_icon1 .d_badge_lv_from {
    background: none;
}
.d_badge_bright {
    border: none;
    width: 100px;
    height: 28px;
    line-height: 28px;
}
.core_title_txt {
    padding: 0 0 0 20px;
}
.l_badge {
    background:none;
    border: none;
}
.d_badge_lv {
    left: 0px !important;
    padding-top: 0px !important;
    margin-top: 0px !important;
    padding-left: 0px !important;
    font-size: 11px;
    display: inline-block;
    text-align: center;
    color: #fff;
    width: 60px;
    position: relative;
    height: 24px;
    border-radius: 30px;
    line-height: 24px;
    background: #0A8581 !important;
    border: 0px solid #cdcbd4;
    /* box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2); */
}
.d_badge_lv::before {
    content:'Lv ';
    position:absolute
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

}
.lzl_p_p {
    float: left;
    width: 32px;
    height: 32px;
    outline: 2px #333 solid !important;
    padding: 0;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
    border-radius: 33px;
    overflow: hidden;
    border: none;
}
.recommend_outtest_container {
    right: 10%;
    margin-right: 0px;;
    margin-left: 0px;
    width: 360px;
    background: #fff;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.3);
    border: none;
}
.p_favthr_tip {
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}
/*按钮*/
.btn_small, .btn-small {
    padding: 6px 8px;
    border-radius: 6px;
    background-color: #0A8581;
    font-size: 12px;
    color: #fff !important;
    border: none;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
}
.btn_sub:hover, .btn-sub:hover, .btn-sub-b:hover, .btn_sub:active, .btn-sub:active, .btn-sub-b:active, .btn_sub:focus, .btn-sub:focus, .btn-sub-b:focus {
    background-color: #0A8581;
    border: none;
    color: #fff;
    border-radius: 6px;
}
.pager_theme_5 a, .pager_theme_5 span {
	color: #fff;
	background: #0a8581;
	border: none;
}
.jump_input_bright {
    width: 25px;
    height: 16px;
    border: 1px solid #999;
    border-radius: 6px;
}
.j_lzl_c_b_a.core_reply_content .edui-container {
    width: 750px !important;
}
.dialogJshadow {
    padding: 0;
    background: none;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.3);
}




/*-----------发帖页------------*/
.frs_content_footer_pagelet {
    width: 980px;
    margin-left: auto;
    border: 0px solid #666;
    margin-right: auto;
    background: none;
    padding-bottom: 26px;
}
.pb_footer {
    background: none;
    border-left: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
    width: 978px;
    border: 0px solid #666;
}
.tb_rich_poster .poster_body .old_style_wrapper {
    width: 940px;
}

.thread_theme_7 {
    padding: 10px 0;
    width: 978px;
    height: 24px;
    background: #fff;
    border: none;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
    border: 0px solid #666;
}
/*---------回复框--------------*/
.tb_rich_poster_container {
    margin: 0;
    padding: 20px 0 0;
    background-color: #fff;
    width: 978px;
    border: 0px solid #666;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.3);
}
.tb_rich_poster .poster_body .old_style_wrapper {
    width: 910px;
}
.tb_rich_poster_container .tb_rich_poster .poster_head {
    margin: 0 240px 0px 0;
    padding: 18px 0 0;
}
.edui-container {
    width: 935px !important;
}
.edui-container .edui-toolbar {
    background-color: transparent;
}
.old_style_wrapper {
    padding: 0;
    border: 0px solid #dfdfdf;
    background: transparent;
    width: 910px !important;
}
.edui-editor-body {
    border: 1px solid #cdcbd4 !important;
}
.tb_rich_poster .poster_signature {
    margin: -12px 0 14px 0;
    height: 20px;
    line-height: 20px;
}
.ui_btn {
    background: #0a8581 !important;
    border: none;
    border-radius: 6px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
}
.poster-right-area {
    position: absolute;
    bottom: 10px;
    right: 20px;
}
.tb_rich_poster .poster_body .editor_bottom_panel {
    margin: 0 20px 0px 0;
    height: 28px;
}
.save-to-quick-reply-btn {
    font-size: 13px;
    color: #fff;
    border: none;
    box-shadow: none;
    border-radius: 6px;
    background: #0a8581;
}
.save-to-quick-reply-btn span {
    color: #fff;
}
.replace_div .replace_tip {
    background-color: #000;
    border: none;
    border-top: 0;
    color: #fff;
}
.edui-editor-body .edui-body-container {
    width: 99% !important;
}


`;

	const STYLE_ID = "tieba-beautify-styles";

	// 样式注入函数
	function injectStyles() {
		// 避免重复注入
		if (document.getElementById(STYLE_ID)) return;

		// 优先使用 GM_addStyle
		if (typeof GM_addStyle === "function") {
			try {
				GM_addStyle(customStyles);
				return;
			} catch {} // 静默失败，转用备选方案
		}

		// 备选方案：创建 style 元素
		const styleEl = document.createElement("style");
		styleEl.id = STYLE_ID;
		styleEl.textContent = customStyles;

		// 优化注入时机
		if (document.head) {
			requestAnimationFrame(() => document.head.appendChild(styleEl));
		} else {
			// 仅在必要时使用 MutationObserver
			const observer = new MutationObserver((mutations, obs) => {
				if (document.head) {
					requestAnimationFrame(() => document.head.appendChild(styleEl));
					obs.disconnect();
				}
			});
			observer.observe(document.documentElement, { childList: true });
		}
	}

	// 在 DOMContentLoaded 之前注入
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", injectStyles, { once: true });
	} else {
		injectStyles();
	}
})();

(function () {
	"use strict";

	// 等待页面加载完成
	window.addEventListener("load", function () {
		// 查找所有的 .replace_tip 元素
		const replaceTips = document.querySelectorAll(".replace_tip");

		// 遍历每一个 .replace_tip 元素
		replaceTips.forEach(function (replaceTip) {
			// 查找每个 .replace_tip 内部的 .icon-expand 图标
			const iconExpand = replaceTip.querySelector(".icon-expand");

			if (iconExpand) {
				// 模拟点击，触发展开
				replaceTip.click();
			}
		});

		// 恢复页面可见性
		document.body.classList.add('script-ready');
	});
})();
