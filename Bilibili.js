// ==UserScript==
// @name         Bilibili
// @version      1.0
// <AUTHOR>
// @match        *://*.bilibili.com/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
	"use strict";
	// 自定义样式定义
	const customStyles = `

body {
    background-color: #E5EEF1 !important;
}

/*-------播放器----------*/
#bilibili-player,
#bilibili-player-placeholder {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 1px 12px rgba(0, 0, 0, 0.1);
}
.bili-dyn-live-users,
.bili-dyn-up-list,
.bili-dyn-list-tabs,
.bili-dyn-my-info,
.bili-dyn-item {
    border-radius: 10px !important;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important;
}


/*-------动态页----------*/
:root {
	--bili-comments-font-size-content: 15px !important;
}
#app .bgc,
#app .bg,
.bili-dyn-home--member {
    background: #E5EEF1 !important;
}
.mini-header {
   background: rgba(255, 255, 255, 0.5) !important;
    position: relative;
}
.mini-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit; /* 继承父元素的背景 */
    backdrop-filter: blur(50px);  /* 模糊背景 */
    z-index: -1 !important; /* 确保伪元素位于内容下方 */
}
.bili-dyn-item {
    margin-bottom: 15px !important;
}
.bili-header .search-panel {
    box-shadow: 0 1px 36px rgba(0, 0, 0, 0.2);
    border: none !important;
    border-radius: 8px !important;
    top: 139%;
    position: absolute;
}
.bili-header .header .title {
    font-size: 15px!important;
}
.bili-header .histories .history-item {
    background: #f1f2f3!important;
    border-radius: 8px!important;
}
.mini-header .center-search-container .center-search__bar #nav-searchform.is-focus {
    border: none !important;
    border-bottom: none !important;
    background: none !important;
}
.bili-header .center-search-container .center-search__bar #nav-searchform {
    line-height: 36px !important;
    border: none!important;
    height: 36px!important;
    opacity: 1!important;
    background: rgba(0, 0, 0, .1) !important;
}
.bili-header .center-search-container .center-search__bar .nav-search-content .nav-search-input:focus {
    background: none !important;
}
.bili-header .center-search-container .center-search__bar.is-focus {
    box-shadow: none !important;
}
.bili-header .center-search-container {
    height: 34px !important;
}

.bili-header .center-search-container .center-search__bar #nav-searchform.is-actived .nav-search-content, .bili-header .center-search-container .center-search__bar #nav-searchform.is-focus .nav-search-content {
    background-color: #61666d29!important;
}
.v-popover-content {
    background: rgba(255, 255, 255, 0.8) !important;
    box-shadow: 0 0 16px rgba(0, 0, 0, 0.2) !important;
    border: none !important;
    backdrop-filter: blur(20px);
}
.bili-header .avatar-panel-popover {
    background: transparent !important;
}
.v-popover {
    transition: .1s !important;
}
.bili-header .message-entry-popover .message-inner-list__item {
    padding: 10px!important;
    color: #333!important;
    font-size: 14px!important;
    transition: background-color .1s !important;
    border-radius: 8px!important;
    margin: 0 10px!important;
}
.bili-header .message-entry-popover .message-inner-list__item:hover {
    background-color: rgba(0, 0, 0, 0.1)!important;
}
.v-popover.is-right {
    background: rgba(255, 255, 255, .7)!important;
}
.bili-user-profile {
    box-shadow: 0 0 30px 2px rgba(0,0,0,.2)!important;
}
.b-avatar__layer.center {
    box-shadow: 0 1px 16px rgba(0, 0, 0, 0.4);
    border: 3px solid #000;
    border-radius: 50% !important;
    width: 48px !important;
    height: 48px !important;
}
.dynamic-all .history-tip[data-v-3a378ba6] {
    display: none !important;
}
.dynamic-all .split-line[data-v-3a378ba6]:before {
    border-top: none !important;
}
.bili-header .header-dynamic-list-item[data-v-1e1051ac]:hover {
    background: rgba(0, 0, 0, .1) !important;
}
.bili-header .header-dynamic-list-item[data-v-1e1051ac] {
    margin: 0 10px;
    border-radius: 8px !important;
}
.bili-header .header-history-video:hover {
    background: rgba(0, 0, 0, .1) !important;
}
.bili-header .header-history-video {
    margin: 0 10px!important;
    border-radius: 8px !important;
}
.history-panel-popover .header-tabs-panel__item--active {
    border-bottom: none !important;
}
.history-panel-popover .header-tabs-panel {
    border-bottom: none !important;
}
.history-panel-popover .header-history-card__info--date, .history-panel-popover .header-history-card__info--name {
    color: #515863 !important;

}
.bili-cascader-options {
    border: none!important;
    border-radius: 8px!important;
    box-shadow: 0 0 16px rgba(0,0,0,.1)!important;
    max-height: 300px!important;
    min-width: 86px!important;
    padding: 12px 4px !important;
    background: rgba(255, 255, 255, 1) !important;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.bili-cascader-options__item:not(.is-disabled):hover {
    background-color: #f1f2f3;
    border-radius: 8px;
}
.bili-cascader-options__item {
    color: #333 !important;
}
.bili-header .bili-header-channel-panel .channel-panel__column {
    border-right: none !important;
}
.v-popover.is-bottom {
    top: 105% !important;
}
::-webkit-scrollbar {
    display: none !important;
}
.dynamic-panel-popover .header-tabs-panel__content {
    max-height: 670px !important;
}
.history-panel-popover .header-tabs-panel__content {
    height: 640px !important;
}
.history-panel-popover {
    height: 700px !important;
}
.bili-header .header-history-video__image .v-img img {
    border-radius: 6px !important;
}
.bili-header .header-history-video__progress {
    display: none !important;
}
.v-img {
    background-color: transparent !important;
}
.history-panel-popover .header-history-card__info--date, .history-panel-popover .header-history-card__info--name {);
    font-size: 11px !important;
    line-height: 17px !important;
}


/*-------搜索页----------*/
.search-input-container .search-input-wrap {
    border-radius: 10px !important;
}
.bili-video-card .bili-video-card__info--tit[data-v-98b7e558]:hover {
    color: none!important;
}
.bili-video-card__wrap {
    background: transparent;
    border-radius: 8px;
    width: 100%;
    min-width: 0;
    max-width: 100%;
   transition: box-shadow 0.35s cubic-bezier(.4,2,.6,1),
                transform 0.35s cubic-bezier(.4,2,.6,1),
                background 0.35s,
                border 0.35s;
}
.bili-video-card__wrap:hover {
	outline: 8px solid #F9F9FC;
	 background: #F9F9FC !important;
	  box-shadow: 0 0 0 4px rgba(64,156,255,0.10), 0 8px 40px 0 rgba(64,156,255,0.18), 0 1.5px 30px rgba(0, 0, 0, 0.10) !important;
    filter: brightness(1.08) saturate(1.12);
    transform: scale(1.06);
    position: relative;
    will-change: transform, box-shadow, border;
    transition: box-shadow 0.35s cubic-bezier(.4,2,.6,1),
                transform 0.35s cubic-bezier(.4,2,.6,1),
                background 0.35s,
                border 0.35s;
}
.col_3 {
    flex: 0 0 300px!important;
    max-width: 30%!important;
    min-width: 300px!important;
}
.recommended-container_floor-aside .container {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr)) !important;
    grid-gap: 50px 30px !important;
}
html[homepage-layout-4-column] #i_cecream .recommended-container_floor-aside .container {
    grid-template-columns: repeat(5,1fr)!important;
}



/*-------首页----------*/
#i_cecream {
	background-color: #F2F2F9;
}
.video-page-card-small {
    margin-bottom: 20px!important;
}
.video-page-card-small .card-box .pic-box {
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
    outline: 2px solid #fff;
}
.video-page-card-small .card-box .info .title {
    font-size: 14px !important;
}

.video-page-card-small .card-box .info .upname {
    font-size: 13px !important;
}
.video-page-card-small .card-box .info .playinfo{
    font-size: 12px!important;
}

.text-\[15px\] {
	font-size: 12px;
}
.bili-video-card .bili-video-card__cover {
	box-shadow: 0 1px 15px rgba(0, 0, 0, 0.15);
}
.bili-video-card .bili-video-card__info--icon-text {
	color: #FFF !important;
	background-color: #979ca2 !important;
	border-radius: 6px !important;
	margin-right: 4px;
	font-size: 11px !important;
	line-height: 15px !important;
	height: 19px !important;
	padding: 2px 8px !important;
}
.bili-video-card .bili-video-card__info--owner {
	color: #565D69;
}
.bili-video-card a:not(.disable-hover):hover {
	color: #18191C;
}
  `;


	const STYLE_ID = "bilibili-enhanced-styles";

	// 样式注入函数
	function injectStyles() {
		// 避免重复注入
		if (document.getElementById(STYLE_ID)) return;

		// 优先使用 GM_addStyle
		if (typeof GM_addStyle === "function") {
			try {
				GM_addStyle(customStyles);
				return;
			} catch {} // 静默失败，转用备选方案
		}

		// 备选方案：创建 style 元素
		const styleEl = document.createElement("style");
		styleEl.id = STYLE_ID;
		styleEl.textContent = customStyles;

		// 优化注入时机
		if (document.head) {
			requestAnimationFrame(() => document.head.appendChild(styleEl));
		} else {
			// 仅在必要时使用 MutationObserver
			const observer = new MutationObserver((mutations, obs) => {
				if (document.head) {
					requestAnimationFrame(() => document.head.appendChild(styleEl));
					obs.disconnect();
				}
			});
			observer.observe(document.documentElement, { childList: true });
		}
	}

	// 在 DOMContentLoaded 之前注入
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", injectStyles, { once: true });
	} else {
		injectStyles();
	}
})();
