// ==UserScript==
// @name         V2EX
// @description  V2EX内容过滤
// <AUTHOR>
// @version      1.0
// @match        http*://www.v2ex.com/*
// @match        http*://*.v2ex.com/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at      document-start
// ==/UserScript==

(function () {
	"use strict";

	// 自定义样式定义
	const customStyles = `
.small.fade img,
.fa.fa-tag,
.page_input,
.votes,
#Main > div:nth-child(4) > div:nth-child(2) > table > tbody > tr > td:nth-child(2) > table,
#Main > div:nth-child(4) > div > table > tbody > tr > td:nth-child(2) > table,
div[style*="border-bottom: 1px solid"],
#Rightbar > div:nth-child(6),
#Rightbar > div:nth-child(8),
#Rightbar > div:nth-child(9) > div.inner,
#SecondaryTabs,
#Main > div:nth-child(7),
#TopicsHot{
    display: none !important;
}
a,
a:hover {
    text-decoration: none !important;
}
#Wrapper {
    background-color: #e1e4e6 !important;
    background-image: none !important;
}
#Top {
    border-bottom: 1px solid rgb(187 199 207);
}
#SecondaryTabs {
    background-color: transparent;
    padding: 10px 10px 10px 20px;
}
#search-container {
    background-color: #f0f0f0;
    border:none;
    font-size: 11px;
}
#search-container #search {
    font-size: 16px;
    background-color: transparent;
    border: none;
    font-size: 13px;
}
a.tab_current:active, a.tab_current:link, a.tab_current:visited {
    border-radius: 6px;
    background-color: #0c0c11;
}
.box {
    border-radius: 10px;
    border-bottom: none;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
    border: none !important;
}
#Tabs {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}
#Main > .box > .cell.item {
    border-bottom: none;
    padding: 0 20px;
}
a.topic-link:active, a.topic-link:link {
    color: #202734;
    font-size: 15px;
}
a.node:active, a.node:link, a.node:visited {
    background-color: transparent;
    color: #778087;
    border-radius: 4px;
}
a.count_livid:active, a.count_livid:link {
    color: #fff;
    background-color: #000;
    font-size: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}
a.dark:active, a.dark:link, a.dark:visited {
    color: #000;
}
img.avatar {
    border-radius: 6px;
    outline: 2px solid #fff;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.3);
}
.topic_info {
    font-size: 12px;
    color: #778087;
    line-height: 200%;
}
#Main > .box > .cell.item > table {
    padding: 22px 0;
}
.cell {
    padding: 20px 25px;
    font-size: 14px;
    line-height: 150%;
    text-align: left;
    border-bottom: none;
}
#Rightbar .cell {
    padding: 10px;
}
.inner {
    font-size: 12px;
}
.item_node {
    font-size: 12px;
    line-height: 14px;
    padding: 4px 10px 4px 10px;
    margin: 0 5px 5px 0;
    border-radius: 6px;
    border: 1px solid #e5e5e5;
    background: #ecedf0;
    color: #888 !important;
}
.fade {
    color: #333;
}
a.count_blue:visited, a.count_green:visited, a.count_livid:visited, a.count_orange:visited {
	background-color: #000;
}
a.topic-link:visited {
	color: #202734;
}
/*-------内页--------*/
.header {
    border-bottom: none;
}
.no {
    color: #fff;
    font-size: 10px;
    background-color: #000;
    padding: 4px 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}
.tag:link, .tag:visited {
    padding: 5px 10px 5px 10px;
    line-height: 100%;
    background-color: #ECEDF0;
    border-radius: 10px;
    margin: 0 5px 0 5px;
    display: inline-block;
    color: #888;
    font-size: 11px;
    border: 1px solid #e0e1e4;
}
table > tbody > tr > td {
    padding: 0 4px 0 0;
    vertical-align: top;
}
span.gray {
    font-size: 12px;
}
h1 {
    padding: 0 8px;
}
.badge.op {
    background-color: transparent;
    color: #fff;
}
.badge:last-child {
    border-right: 1px solid #52bf1c;
}
.badge:first-child {
    border-left: 1px solid #52bf1c;
    background: #52bf1c;
}
.badge {
    border-top: 1px solid #52bf1c;
    border-bottom: 1px solid #52bf1c;
}
.subtle {
    background-color: #eefeeb;
    border-left: none;
    margin: 0 20px 20px 20px;
    font-size: 14px;
    line-height: 120%;
    text-align: left;
    border-bottom: none;
    border-radius: 8px;
    outline: 1px solid #c9e2c3;
}
.ago {
    font-size: 11px;
    color: #778087;
    cursor: pointer;
}
.ps_container {
    background-image: none;
    background-size: 20px 20px;
    background-repeat: repeat-x;
}
.page_current:is(:link, :visited) {
    pointer-events: none;
    background-color: #000;
    box-shadow: none;
    border: none;
    color: #fff;
    border-radius: 4px;
    font-size: 12px;
    padding: 3px 6px;
    line-height: 13px;
}
.page_normal:hover {
    color: #000;
}
a:active, a:link, a:visited {
    color: #666;
}
.page_normal:active, .page_normal:link, .page_normal:visited {
    display: inline-block;
    font-size: 12px;
    line-height: 13px;
    padding: 3px 5px;
    background-color: #e0e0e0;
    border-radius: 4px;
    margin: 0 1px;
    border: none;
    box-shadow: none;
}
table > tbody > tr > td > span.small.fade {
    background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyBoZWlnaHQ9IjE4cHgiIHdpZHRoPSIxOHB4IiB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIuMDAyIDUxMi4wMDIiPgogIDxwYXRoIHN0eWxlPSJmaWxsOiByZ2IoMjU0LCAyMjYsIDIyNSk7IiBkPSJNMjU1Ljk5OCw0NzQuNTA2bDIwMS45NTgtMjAxLjk1OGMyNi41NTYtMjYuNTU2LDQxLjE4Mi02MS45MDksNDEuMTgyLTk5LjU1OCBzLTE0LjYyNi03My4wMDMtNDEuMTgyLTk5LjU1OGMtMjYuNTktMjYuNTk4LTYxLjk0My00MS4yNDItOTkuNTU4LTQxLjI0MmMtMzMuNDkzLDAtNjYuMjEsMTIuNDQyLTkyLjQzMywzNS4zMzdsLTkuOTY3LDkuMzI3IGwtMTAuMjkxLTkuNjE3QzIxOS44MDgsNDQuNjMsMTg3LjEsMzIuMTg5LDE1My41OTgsMzIuMTg5Yy0zNy42MTUsMC03Mi45NjksMTQuNjQzLTk5LjU1OCw0MS4yNDIgYy0yNi41NTYsMjYuNTU2LTQxLjE4Miw2MS45MDktNDEuMTgyLDk5LjU1OFMyNy40ODQsMjQ2LDU0LjAzOSwyNzIuNTQ3TDI1NS45OTgsNDc0LjUwNnoiLz4KICA8cGF0aCBzdHlsZT0iZmlsbDogcmdiKDIzOSwgNjgsIDY4KTsiIGQ9Ik0yNTUuOTk4LDQ5Mi42MTNMNDY3LjAxLDI4MS42MDFDNTI3LDIyMS42MTIsNTI3LDEyNC4zNjYsNDY3LjAxLDY0LjM3NyBjLTI5Ljk5NS0yOS45OTUtNjkuMjk5LTQ0Ljk4OC0xMDguNjEyLTQ0Ljk4OGMtMzYuNzc5LDAtNzMuMjU5LDEzLjY2Mi0xMDIuNCwzOS45MTljLTI5LjE1LTI2LjI1Ny02NS42MjEtMzkuOTE5LTEwMi40LTM5LjkxOSBjLTM5LjMxMywwLTc4LjYxOCwxNC45OTMtMTA4LjYxMiw0NC45ODhjLTU5Ljk4MSw1OS45ODEtNTkuOTgxLDE1Ny4yMzUsMCwyMTcuMjI1TDI1NS45OTgsNDkyLjYxM3ogTTYzLjA4NSw4Mi40NzYgYzI0LjE3NS0yNC4xNzUsNTYuMzItMzcuNDg3LDkwLjUxMy0zNy40ODdjMzEuMjA2LDAsNjAuMzk5LDExLjU2Myw4My42OTUsMzEuODg5bDE4LjcwNSwxNy40OTNsMTguNzE0LTE3LjQ5MyBjMjMuMjk2LTIwLjMxOCw1Mi40ODktMzEuODg5LDgzLjY4Ni0zMS44ODljMzQuMTkzLDAsNjYuMzMsMTMuMzEyLDkwLjUxMywzNy40ODdjNDkuOTExLDQ5LjkwMyw0OS45MDMsMTMxLjExNSwwLDE4MS4wMTggTDI1NS45OTgsNDU2LjQwNkw2My4wODUsMjYzLjUwMkMxMy4xODIsMjEzLjU5LDEzLjE4MiwxMzIuMzg3LDYzLjA4NSw4Mi40NzZ6Ii8+Cjwvc3ZnPg==');
    background-repeat: no-repeat;
    width: 14px;
    height: 14px;
    display: inline-block;
    padding: 0px 18px;
    background-size: 14px;
    line-height: 14px;
}
#Main > div:nth-child(2) {
    padding: 0 0 1px !important;
}

/* 屏蔽按钮和触发区域样式 */
.block-list-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    padding: 5px 10px;
    background: #000;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.trigger-area {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    z-index: 999;
}

.trigger-area:hover .block-list-button {
    opacity: 1;
    pointer-events: auto;
}

.block-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-75%, -50%);
    width: 500px;
    max-width: 90vw;
    height: auto;
    max-height: 80vh;
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 1px 40px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    overflow: auto;
}

.block-dialog::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    z-index: -1;
    backdrop-filter: blur(50px);
}

.block-title-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: none;
    cursor: move;
    user-select: none;
}

.block-close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 16px;
    height: 16px;
    background: black;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.15s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    text-indent: -9999px;
    padding: 0;
    z-index: 1002;
}

.block-close-button:hover {
    background: red;
}

.block-close-line {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 2px;
    background: white;
    transition: background 0.15s ease;
}

.block-close-line-1 {
    transform: translate(-50%, -50%) rotate(45deg);
}

.block-close-line-2 {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.block-list {
    margin: 20px 0 0 0;
    max-height: 220px;
    font-size: 12px;
    padding: 10px;
    background: #00000012;
    border-radius: 8px;
    color: #333;
}

.block-keyword-list {
    margin: 15px 0 0 0;
    max-height: 100px;
}

.block-input-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin: 15px 0;
}

.block-input {
    border: 1px solid #dddddd;
    background: #ffffff96;
    border-radius: 8px;
    font-size: 13px;
    padding: 5px 10px;
    width: 180px;
}

.block-input:focus {
    border: 1px solid #999;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1)
}

.block-button {
    border-radius: 8px;
    border: 0px solid black;
    background: #000;
    padding: 5px 10px;
    color: white;
    font-size: 13px;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}
.block-item {
    display: inline-block;
    margin: 0 5px 5px 0;
    background: #f1f2f6;
    padding: 2px 5px;
    border-radius: 4px;
    border: 1px solid #dfe4ea;
}
h3 {
    margin: 0 0 10px;
    font-size: 14px;
    color: #000;

}
.normal.button {
    background-color: #000;
    color: #fff;
    text-shadow: none;
    border-radius: 8px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
}
.super.button {
    background-image: none;
}

/* 用户名屏蔽按钮样式 */
.author-name-container {
    position: relative;
    display: inline-block;
    padding-right: 5px;
    transition: padding-right 0.1s ease-in-out;
}

.author-name-container:hover {
    padding-right: 40px;
}

.block-user-hover-button {
    display: none;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    padding: 0px 7px;
    font-size: 10px;
    background-color: #000;
    color: #fff;
    border-radius: 6px;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0px 1px 2px rgba(0,0,0,0.1);
    white-space: nowrap;
    margin-left: 5px;
}

.author-name-container:hover .block-user-hover-button {
    display: inline-block;
}

.blocked-user-indicator {
    color: grey;
    font-size: 0.9em;
    margin-left: 4px;
}`;

	// 样式注入函数，采用更可靠的多种方法确保样式被应用
	function injectStyles() {
		try {
			// 方法1: 使用GM_addStyle
			GM_addStyle(customStyles);

			// 方法2: 创建样式元素作为备份方法
			const styleEl = document.createElement("style");
			styleEl.id = "v2ex-enhanced-styles";
			styleEl.textContent = customStyles;

			// 先尝试添加到head
			if (document.head) {
				document.head.appendChild(styleEl);
			} else {
				// 如果head还不存在，使用MutationObserver监听等待head出现
				const headObserver = new MutationObserver(() => {
					if (
						document.head &&
						!document.getElementById("v2ex-enhanced-styles")
					) {
						document.head.appendChild(styleEl);
						headObserver.disconnect();
					}
				});
				headObserver.observe(document.documentElement, { childList: true });

				// 同时将样式元素添加到documentElement作为临时解决方案
				document.documentElement.appendChild(styleEl);
			}

			console.log("V2EX增强: 样式已应用");
		} catch (error) {
			console.error("V2EX增强: 样式应用失败", error);
		}
	}

	// 立即注入样式，确保最早应用
	injectStyles();

	// 显示通知函数
	function showNotification(message) {
		// 如果body不存在，将消息存储并稍后显示
		if (!document.body) {
			const checkBodyInterval = setInterval(() => {
				if (document.body) {
					clearInterval(checkBodyInterval);
					createNotification(message);
				}
			}, 100);
			return;
		}

		createNotification(message);
	}

	function createNotification(message) {
		const notification = document.createElement("div");
		notification.style.position = "fixed";
		notification.style.bottom = "20px";
		notification.style.left = "20px";
		notification.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
		notification.style.color = "#fff";
		notification.style.padding = "10px 20px";
		notification.style.borderRadius = "8px";
		notification.style.zIndex = "9999";
		notification.style.transition = "opacity 0.3s ease";
		notification.style.opacity = "0";
		notification.textContent = message;

		document.body.appendChild(notification);

		// 显示通知
		setTimeout(() => {
			notification.style.opacity = "1";
		}, 10);

		// 3秒后淡出
		setTimeout(() => {
			notification.style.opacity = "0";
			setTimeout(() => {
				if (document.body.contains(notification)) {
					document.body.removeChild(notification);
				}
			}, 300);
		}, 3000);
	}

	// 安全的存储操作函数
	function safeGetValue(key, defaultValue) {
		try {
			return GM_getValue(key, defaultValue) || defaultValue;
		} catch (error) {
			console.error(`Error getting value for ${key}:`, error);
			return defaultValue;
		}
	}

	function safeSetValue(key, value) {
		try {
			GM_setValue(key, value);
			return true;
		} catch (error) {
			console.error(`Error setting value for ${key}:`, error);
			showNotification(`保存失败: ${error.message}`);
			return false;
		}
	}

	// 获取存储的屏蔽列表
	const blockedUsers = safeGetValue("blockedUsers", []);
	const blockedKeywords = safeGetValue("blockedKeywords", []);

	// 防抖函数
	function debounce(func, wait) {
		let timeout;
		return function (...args) {
			clearTimeout(timeout);
			timeout = setTimeout(() => func.apply(this, args), wait);
		};
	}

	// 创建触发区域和按钮
	function createBlockButton() {
		// 防止重复创建
		if (document.querySelector(".trigger-area")) {
			return;
		}

		const triggerArea = document.createElement("div");
		triggerArea.className = "trigger-area";

		const button = document.createElement("button");
		button.innerText = "屏蔽列表";
		button.className = "block-list-button";
		button.addEventListener("click", showBlockListDialog);

		triggerArea.appendChild(button);
		document.body.appendChild(triggerArea);
	}

	// 显示屏蔽列表对话框
	function showBlockListDialog() {
		// 检查是否已存在对话框
		if (document.querySelector(".block-dialog")) {
			return;
		}

		const dialog = document.createElement("div");
		dialog.className = "block-dialog";

		// 创建标题栏作为拖动区域
		const titleBar = document.createElement("div");
		titleBar.className = "block-title-bar";

		// 拖动实现
		let isDragging = false;
		let startX, startY;
		let initialLeft, initialTop;

		function startDragging(e) {
			if (e.target === titleBar) {
				isDragging = true;
				startX = e.clientX;
				startY = e.clientY;

				const rect = dialog.getBoundingClientRect();
				initialLeft = rect.left;
				initialTop = rect.top;

				dialog.style.transform = "none";
				dialog.style.left = `${initialLeft}px`;
				dialog.style.top = `${initialTop}px`;
			}
		}

		function doDrag(e) {
			if (!isDragging) return;

			e.preventDefault();
			const deltaX = e.clientX - startX;
			const deltaY = e.clientY - startY;

			dialog.style.left = `${initialLeft + deltaX}px`;
			dialog.style.top = `${initialTop + deltaY}px`;
		}

		function stopDragging() {
			isDragging = false;
		}

		titleBar.addEventListener("mousedown", startDragging);
		document.addEventListener("mousemove", doDrag);
		document.addEventListener("mouseup", stopDragging);

		// 创建关闭按钮
		const closeButton = document.createElement("button");
		closeButton.className = "block-close-button";
		closeButton.innerHTML =
			'<span class="block-close-line block-close-line-1"></span><span class="block-close-line block-close-line-2"></span>';
		closeButton.onclick = () => {
			document.removeEventListener("mousemove", doDrag);
			document.removeEventListener("mouseup", stopDragging);
			document.body.removeChild(dialog);
		};

		// 显示当前屏蔽的用户名列表
		const userList = document.createElement("div");
		userList.className = "block-list";
		// 获取最近的70个用户名
		const recentBlockedUsers = blockedUsers.slice(-70);
		userList.innerHTML = `<h3 class="block-title">屏蔽的用户名 (显示最近70个):</h3>${
			recentBlockedUsers.length > 0
				? recentBlockedUsers.join(", ")
				: "<div style='font-style:italic;color:#999'>暂无屏蔽用户</div>"
		}`;

		// 创建用户名输入区域
		const userInputContainer = document.createElement("div");
		userInputContainer.className = "block-input-container";

		const userInput = document.createElement("input");
		userInput.type = "text";
		userInput.placeholder = "输入用户名";
		userInput.className = "block-input";

		const addUserButton = document.createElement("button");
		addUserButton.innerText = "添加用户名";
		addUserButton.className = "block-button";

		// 显示当前屏蔽的关键词列表
		const keywordList = document.createElement("div");
		keywordList.className = "block-list block-keyword-list";
		keywordList.innerHTML = `<h3 class="block-title">屏蔽的关键词:</h3>${
			blockedKeywords.length > 0
				? blockedKeywords.join(", ")
				: "<div style='font-style:italic;color:#999'>暂无屏蔽关键词</div>"
		}`;

		// 创建关键词输入区域
		const keywordInputContainer = document.createElement("div");
		keywordInputContainer.className = "block-input-container";

		const keywordInput = document.createElement("input");
		keywordInput.type = "text";
		keywordInput.placeholder = "输入关键词";
		keywordInput.className = "block-input";

		const addKeywordButton = document.createElement("button");
		addKeywordButton.innerText = "添加关键词";
		addKeywordButton.className = "block-button";

		// 添加用户名事件
		addUserButton.onclick = () => {
			const newUser = userInput.value.trim();
			if (newUser) {
				if (blockedUsers.includes(newUser)) {
					showNotification(`用户 "${newUser}" 已在屏蔽列表中`);
					return;
				}

				blockedUsers.push(newUser);
				if (safeSetValue("blockedUsers", blockedUsers)) {
					const recentBlockedUsers = blockedUsers.slice(-70);
					userList.innerHTML = `<h3 class="block-title">屏蔽的用户名 (显示最近70个):</h3>${recentBlockedUsers.join(
						", "
					)}`;
					userInput.value = "";
					debouncedFilterContent();
					showNotification(`已添加屏蔽用户: ${newUser}`);
				}
			}
		};

		// 添加关键词事件
		addKeywordButton.onclick = () => {
			const newKeyword = keywordInput.value.trim();
			if (newKeyword) {
				if (blockedKeywords.includes(newKeyword)) {
					showNotification(`关键词 "${newKeyword}" 已在屏蔽列表中`);
					return;
				}

				blockedKeywords.push(newKeyword);
				if (safeSetValue("blockedKeywords", blockedKeywords)) {
					keywordList.innerHTML = `<h3 class="block-title">屏蔽的关键词:</h3>${blockedKeywords.join(
						", "
					)}`;
					keywordInput.value = "";
					debouncedFilterContent();
					showNotification(`已添加屏蔽关键词: ${newKeyword}`);
				}
			}
		};

		// 添加回车键提交
		userInput.addEventListener("keypress", (e) => {
			if (e.key === "Enter") {
				addUserButton.click();
			}
		});

		keywordInput.addEventListener("keypress", (e) => {
			if (e.key === "Enter") {
				addKeywordButton.click();
			}
		});

		// 组装界面元素
		userInputContainer.appendChild(userInput);
		userInputContainer.appendChild(addUserButton);
		keywordInputContainer.appendChild(keywordInput);
		keywordInputContainer.appendChild(addKeywordButton);

		dialog.appendChild(titleBar);
		dialog.appendChild(closeButton);
		dialog.appendChild(userList);
		dialog.appendChild(userInputContainer);
		dialog.appendChild(keywordList);
		dialog.appendChild(keywordInputContainer);

		document.body.appendChild(dialog);
	}

	// 检查内容是否应该被屏蔽
	function shouldBlockContent(username, content) {
		// 检查用户名
		if (username && blockedUsers.some((user) => username.includes(user))) {
			return true;
		}

		// 检查内容关键词
		if (
			content &&
			blockedKeywords.some((keyword) =>
				content.toLowerCase().includes(keyword.toLowerCase())
			)
		) {
			return true;
		}

		return false;
	}

	// 过滤内容
	function filterContent() {
		// 首页过滤
		$(".cell.item").each(function () {
			const $item = $(this);
			const username = $item
				.find(".topic_info strong a, .topic_info .dark")
				.text()
				.trim();
			const title = $item.find(".item_title a").text().trim();

			if (shouldBlockContent(username, title)) {
				$item.remove();
			}
		});

		// 帖子内页回复过滤
		$(".cell").each(function () {
			if ($(this).attr("id") && $(this).attr("id").startsWith("r_")) {
				const $reply = $(this);
				const username = $reply.find(".dark, strong a.dark").text().trim();
				const content = $reply.find(".reply_content").text().trim();

				if (shouldBlockContent(username, content)) {
					$reply.remove();
				}
			}
		});
	}

	// 在用户名旁边添加屏蔽按钮
	function addBlockUserButtonsToUsernames(parentElement = document) {
		// V2EX 用户名选择器
		const usernameSelectors = [
			".topic_info strong a", // 首页帖子作者
			".topic_info .dark", // 首页帖子作者（另一种情况）
			".dark, strong a.dark", // 回复作者
			".header small a", // 帖子内页作者
			".fr a.dark", // 其他可能的作者链接
			"a.dark[href^='/member/']" // 通用用户链接
		];

		usernameSelectors.forEach(selector => {
			const userElements = $(selector, parentElement);
			userElements.each(function() {
				const $userElement = $(this);
				// 确保用户名元素有效
				const username = $userElement.text().trim();
				if (!username || username.length === 0 || username.length >= 50) return;

				// 检查是否已经添加了屏蔽按钮
				if ($userElement.parent().hasClass("author-name-container")) return;

				// 创建包裹容器
				const $container = $("<span class='author-name-container'></span>");
				$userElement.wrap($container);

				// 已屏蔽用户的处理
				if (blockedUsers.includes(username)) {
					if (!$userElement.next(".blocked-user-indicator").length) {
						$userElement.after("<span class='blocked-user-indicator'> (已屏蔽)</span>");
					}
					return; // 不为已屏蔽用户添加屏蔽按钮
				}

				// 创建屏蔽按钮
				const $blockButton = $("<span class='block-user-hover-button'>屏蔽</span>");
				$blockButton.attr("title", `屏蔽用户: ${username}`);

				// 添加点击事件
				$blockButton.on("click", function(e) {
					e.preventDefault();
					e.stopPropagation();

					// 执行屏蔽操作
					if (!blockedUsers.includes(username)) {
						blockedUsers.push(username);
						if (safeSetValue("blockedUsers", blockedUsers)) {
							showNotification(`已屏蔽用户: ${username}`);

							// 移除按钮，添加已屏蔽标记
							$blockButton.remove();
							$userElement.after("<span class='blocked-user-indicator'> (已屏蔽)</span>");

							// 重新过滤内容
							debouncedFilterContent();
						}
					} else {
						showNotification(`用户 "${username}" 已在屏蔽列表中。`);
					}
				});

				// 将按钮添加到容器中
				$userElement.parent().append($blockButton);
			});
		});
	}

	// 防抖处理的过滤函数
	const debouncedFilterContent = debounce(filterContent, 300);

	// 检查jQuery是否加载
	function checkJQuery(callback, maxAttempts = 20) {
		if (maxAttempts <= 0) {
			console.error("jQuery加载失败");
			return;
		}

		if (typeof jQuery !== "undefined") {
			callback();
		} else {
			setTimeout(() => checkJQuery(callback, maxAttempts - 1), 200);
		}
	}

	// 初始化
	function init() {
		createBlockButton();
		filterContent();
		addBlockUserButtonsToUsernames(); // 添加用户名屏蔽按钮

		// 监听DOM变化，处理动态加载的内容
		const observer = new MutationObserver((mutations) => {
			// 检查是否有新内容添加
			let hasNewContent = false;
			for (const mutation of mutations) {
				if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
					hasNewContent = true;
					break;
				}
			}

			// 如果有新内容，重新过滤并添加屏蔽按钮
			if (hasNewContent) {
				debouncedFilterContent();
				addBlockUserButtonsToUsernames();
			}
		});
		observer.observe(document.body, { childList: true, subtree: true });

		// 确保样式已应用
		injectStyles();
	}

	// 创建一个早期的observer来确保DOM呈现前就能捕获到变化
	const earlyObserver = new MutationObserver(() => {
		// 再次确保样式已应用
		if (!document.getElementById("v2ex-enhanced-styles")) {
			injectStyles();
		}

		// 检查body是否可用
		if (document.body && !document.querySelector(".trigger-area")) {
			createBlockButton();

			// 如果jQuery已加载，初始化过滤和添加屏蔽按钮
			if (typeof jQuery !== "undefined") {
				filterContent();
				addBlockUserButtonsToUsernames();
			} else {
				// 否则等待jQuery加载
				checkJQuery(init);
			}

			// 完成初始设置后断开早期观察器
			earlyObserver.disconnect();
		}
	});

	// 观察document的变化，等待body出现
	earlyObserver.observe(document, { childList: true, subtree: true });

	// 如果document已完成加载
	if (
		document.readyState === "interactive" ||
		document.readyState === "complete"
	) {
		// 再次确保样式已应用
		injectStyles();

		// 如果body已经存在，直接初始化
		if (document.body) {
			createBlockButton();
			checkJQuery(() => {
				init();
				// 确保用户名屏蔽按钮被添加
				setTimeout(addBlockUserButtonsToUsernames, 500);
			});
			earlyObserver.disconnect();
		}
	} else {
		// 监听DOMContentLoaded事件，再次确保样式应用和初始化
		document.addEventListener("DOMContentLoaded", () => {
			injectStyles();
			if (document.body) {
				createBlockButton();
				checkJQuery(() => {
					init();
					// 确保用户名屏蔽按钮被添加
					setTimeout(addBlockUserButtonsToUsernames, 500);
				});
				earlyObserver.disconnect();
			}
		});
	}

	// 最后的保障：监听load事件，确保一切正常运行
	window.addEventListener("load", () => {
		injectStyles();
		if (document.body && !document.querySelector(".trigger-area")) {
			createBlockButton();
			checkJQuery(() => {
				init();
				// 确保用户名屏蔽按钮被添加
				setTimeout(addBlockUserButtonsToUsernames, 500);
			});
		}
	});

	// 监听滚动事件，处理可能的懒加载内容
	let scrollTimeout;
	window.addEventListener('scroll', () => {
		if (typeof jQuery !== "undefined") {
			clearTimeout(scrollTimeout);
			scrollTimeout = setTimeout(() => {
				addBlockUserButtonsToUsernames();
			}, 500);
		}
	}, { passive: true });
})();
