// ==UserScript==
// @name         ☆ 强制字体
// @namespace    http://tampermonkey.net/
// @version      1.2
// <AUTHOR>
// @description  强制所有网页使用苹方字体，且将小于500字重的文字设置为500字重。
// <AUTHOR>
// @match        *://*/*
// @run-at       document-start
// ==/UserScript==

(function () {
    "use strict";

    // 添加字体样式（优先加载）
    function addStyleSheet() {
        const style = document.createElement("style");
        style.id = "forced-font-style";
        style.textContent = `
/* 强制全局文字字体*/
*:not([class*='nvhm']):not([class*='symbol' i]):not([class*='ico' i]):not([class*='fa-' i]):not([class*='svg' i]):not([class*='emoji' i]):not([class*='ifont' i]):not([class*='icon' i]):not([class*='c-price' i]):not(i):not([class*='ls-caret']):not([class*='mg-wrapper']):not([class*='notice']){
    font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans", "Microsoft YaHei", "SF Pro", sans-serif !important;
}

/* 预设常见元素字重，减少动态计算 */
p, div, span, a, li, button, input, textarea, select, label, td, th {
    font-weight: 500 !important;
}

/* 保持粗体元素的粗体效果 */
b, strong, h1, h2, h3, h4, h5, h6 {
    font-weight: 600 !important;
}
        `;

        // 立即插入样式（不等待head）
        const insertStyleImmediately = () => {
            if (document.head) {
                document.head.appendChild(style);
            } else if (document.documentElement) {
                document.documentElement.appendChild(style);
                // 当head可用时移动到head
                const moveToHead = () => {
                    if (document.head && style.parentElement !== document.head) {
                        document.head.appendChild(style);
                    }
                };
                // 检查head是否已创建
                const headObserver = new MutationObserver(() => {
                    moveToHead();
                    if (document.head) {
                        headObserver.disconnect();
                    }
                });
                headObserver.observe(document.documentElement, { childList: true });
                // 兜底检查，确保样式被移动到head
                setTimeout(moveToHead, 100);
            }
        };

        insertStyleImmediately();
    }

    // 立即添加样式表
    addStyleSheet();

    // 定义选择器
    const targetSelector = "*:not([class*='nvhm']):not([class*='symbol' i]):not([class*='ico' i]):not([class*='fa-' i]):not([class*='svg' i]):not([class*='emoji' i]):not([class*='ifont' i]):not([class*='icon' i]):not([class*='c-price' i]):not(i):not([class*='ls-caret']):not([class*='mg-wrapper']):not([class*='notice'])";

    // 防抖机制
    let debounceTimer = null;
    const debounceDelay = 200; // 减少延迟时间

    // 标记是否已处理过主要内容区域
    let initialProcessDone = false;

    // 优先处理首屏元素
    function processInitialViewport() {
        if (initialProcessDone) return;
        
        // 快速处理可视区域内的关键元素
        const priority = ['h1', 'h2', 'h3', 'button', '.header', '.nav', '.title', 'article', '.content'];
        priority.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                for (let i = 0; i < elements.length; i++) {
                    updateFontWeight(elements[i]);
                }
            } catch (e) {}
        });
        
        // 处理可视区域元素
        processVisibleElements(true);
        initialProcessDone = true;
    }

    // 使用MutationObserver监控DOM变化并应用字重规则
    function applyMinimumFontWeight(immediate) {
        // 清除之前的定时器
        if (debounceTimer) {
            clearTimeout(debounceTimer);
        }

        // 如果是即时模式，直接处理
        if (immediate) {
            processVisibleElements(true);
            return;
        }

        // 创建新的定时器
        debounceTimer = setTimeout(() => {
            // 使用requestAnimationFrame以避免阻塞主线程
            requestAnimationFrame(() => {
                // 处理可见区域内的元素
                processVisibleElements();
                
                // 几秒后处理页面其他部分的元素，优先级降低
                if (document.visibilityState === 'visible') {
                    setTimeout(() => {
                        processRemainingElements();
                    }, 2000);
                }
            });
        }, debounceDelay);
    }
    
    // 处理可见区域内的元素
    function processVisibleElements(isInitial = false) {
        // 获取视口尺寸
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        
        // 首屏区域只处理特定标签以加快速度
        let elements;
        if (isInitial) {
            const fastSelector = 'h1, h2, h3, h4, h5, p, div > span, .header, .banner, .hero, nav, button, a, li';
            elements = document.querySelectorAll(fastSelector);
        } else {
            elements = document.querySelectorAll(targetSelector);
        }
        
        // 使用更快的循环
        const len = Math.min(elements.length, isInitial ? 200 : 500); // 限制首次处理的数量
        for (let i = 0; i < len; i++) {
            const element = elements[i];
            try {
                const rect = element.getBoundingClientRect();
                
                // 检查元素是否在视口或接近视口
                if (rect.bottom >= -100 && rect.top <= viewportHeight + 100 &&
                    rect.right >= -100 && rect.left <= viewportWidth + 100) {
                    updateFontWeight(element);
                }
            } catch (e) {}
        }
    }
    
    // 处理剩余元素
    function processRemainingElements() {
        // 如果用户不在当前页面，暂停处理
        if (document.visibilityState !== 'visible') {
            return;
        }
        
        const elements = document.querySelectorAll(targetSelector);
        
        // 每批处理元素的数量，动态调整批处理大小
        const batchSize = navigator.hardwareConcurrency > 4 ? 150 : 80;
        let index = 0;
        
        function processBatch() {
            // 如果用户不在当前页面，暂停处理
            if (document.visibilityState !== 'visible') {
                return;
            }
            
            const startTime = performance.now();
            const endIndex = Math.min(index + batchSize, elements.length);
            
            for (let i = index; i < endIndex; i++) {
                updateFontWeight(elements[i]);
            }
            
            index = endIndex;
            
            // 计算处理时间以动态调整下一批的延迟
            const processingTime = performance.now() - startTime;
            const nextDelay = processingTime > 20 ? 50 : 0; // 如果处理时间超过20ms，增加延迟
            
            if (index < elements.length) {
                // 继续处理下一批
                setTimeout(processBatch, nextDelay);
            }
        }
        
        processBatch();
    }
    
    // 更新元素的字体粗细
    function updateFontWeight(element) {
        try {
            // 优化：缓存已处理的元素
            if (element.__fontWeightProcessed) return;
            
            const fontWeight = window.getComputedStyle(element).fontWeight;
            // 如果字重是数字值且小于500，或者是normal/lighter等非数字值，则设置为500
            if (fontWeight === 'normal' || fontWeight === 'lighter' || parseInt(fontWeight) < 500) {
                element.style.fontWeight = '500';
                // 标记为已处理
                element.__fontWeightProcessed = true;
            }
        } catch (e) {
            // 忽略错误
        }
    }

    // 配置观察器选项
    const observerOptions = {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    };

    // 页面开始加载时就初始化观察器
    const observer = new MutationObserver(mutations => {
        let shouldUpdate = false;
        
        // 快速检查是否有需要处理的变化
        for (let i = 0; i < Math.min(mutations.length, 20); i++) {
            const mutation = mutations[i];
            
            // 如果有新增节点或样式改变
            if (mutation.addedNodes.length > 0 || 
                (mutation.type === 'attributes' && 
                 (mutation.attributeName === 'style' || mutation.attributeName === 'class'))) {
                shouldUpdate = true;
                break;
            }
        }
        
        if (shouldUpdate) {
            applyMinimumFontWeight(false);
        }
    });

    // 一旦DOM可用就执行初始化处理
    function initializeProcess() {
        processInitialViewport();
        
        // 开始观察
        observer.observe(document.documentElement, observerOptions);
    }

    // 设置初始化处理
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeProcess);
    } else {
        initializeProcess();
    }

    // 处理首屏加载优先级
    if (document.readyState === 'interactive' || document.readyState === 'complete') {
        processInitialViewport();
    } else {
        const readyStateCheck = () => {
            if (document.readyState === 'interactive') {
                processInitialViewport();
                document.removeEventListener('readystatechange', readyStateCheck);
            }
        };
        document.addEventListener('readystatechange', readyStateCheck);
    }

    // 文档可见性变化时的处理
    document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible' && initialProcessDone) {
            applyMinimumFontWeight(true);
        }
    });

    // 在页面滚动时处理新进入视口的元素
    window.addEventListener('scroll', () => {
        if (debounceTimer) clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            processVisibleElements();
        }, 150);
    }, { passive: true });

    // 页面完全加载后再次应用一次
    window.addEventListener('load', () => {
        processVisibleElements(true);
        setTimeout(() => {
            processRemainingElements();
        }, 500);
    });
})();