# 项目最佳实践

## 用户脚本开发最佳实践

### 1. 脚本元数据规范

#### UserScript头部标准
```javascript
// ==UserScript==
// @name         [中文名称] - 简洁描述功能
// @description  详细功能描述，包含主要特性
// <AUTHOR>
// @version      语义化版本号 (如: 1.2.3)
// @match        *://目标域名/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_registerMenuCommand
// @run-at       document-idle
// @namespace    唯一命名空间
// ==/UserScript==
```

#### 版本管理规范
- **主版本号**: 重大功能变更或不兼容更新
- **次版本号**: 新功能添加，向后兼容
- **修订号**: Bug修复和小改进
- **示例**: 1.0.0 → 1.1.0 → 1.1.1

### 2. 代码结构最佳实践

#### 标准脚本结构
```javascript
(function () {
    "use strict";
    
    // 1. 防止在iframe中执行
    if (window.self !== window.top) {
        return;
    }
    
    // 2. 全局变量声明
    const SCRIPT_NAME = '脚本名称';
    const VERSION = '1.0.0';
    const DEBUG = false;
    
    // 3. 工具函数
    const utils = {
        log: (msg) => DEBUG && console.log(`[${SCRIPT_NAME}] ${msg}`),
        error: (msg) => console.error(`[${SCRIPT_NAME}] ${msg}`)
    };
    
    // 4. 配置管理
    const defaultConfig = {
        enabled: true,
        autoRun: true
    };
    
    // 5. 主要功能模块
    const modules = {
        init() {
            this.loadConfig();
            this.setupUI();
            this.bindEvents();
        }
    };
    
    // 6. 初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', modules.init.bind(modules));
    } else {
        modules.init();
    }
})();
```

### 3. 性能优化最佳实践

#### DOM操作优化
```javascript
// ✅ 好的做法：批量DOM操作
function addMultipleElements(items) {
    const fragment = document.createDocumentFragment();
    items.forEach(item => {
        const element = createItemElement(item);
        fragment.appendChild(element);
    });
    container.appendChild(fragment);
}

// ❌ 避免：频繁DOM操作
function addElementsOneByOne(items) {
    items.forEach(item => {
        const element = createItemElement(item);
        container.appendChild(element); // 每次都触发重排
    });
}
```

#### 事件处理优化
```javascript
// ✅ 好的做法：事件委托
document.addEventListener('click', function(e) {
    if (e.target.matches('.action-button')) {
        handleAction(e.target);
    }
});

// ✅ 好的做法：防抖处理
const debouncedSearch = debounce(function(query) {
    performSearch(query);
}, 300);

// 防抖函数实现
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
```

### 4. 错误处理最佳实践

#### 全局错误处理
```javascript
// 全局错误捕获
window.addEventListener('error', function(e) {
    utils.error(`未捕获的错误: ${e.message}`);
    // 可选：发送错误报告
});

// Promise错误处理
window.addEventListener('unhandledrejection', function(e) {
    utils.error(`未处理的Promise拒绝: ${e.reason}`);
    e.preventDefault();
});
```

#### 函数级错误处理
```javascript
// ✅ 好的做法：完整错误处理
async function safeAsyncOperation() {
    try {
        const result = await riskyOperation();
        return { success: true, data: result };
    } catch (error) {
        utils.error(`操作失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// ✅ 好的做法：输入验证
function processUserInput(input) {
    if (!input || typeof input !== 'string') {
        throw new Error('无效的输入参数');
    }
    
    const sanitized = input.trim();
    if (sanitized.length === 0) {
        throw new Error('输入不能为空');
    }
    
    return sanitized;
}
```

### 5. 数据存储最佳实践

#### 配置管理模式
```javascript
const ConfigManager = {
    // 默认配置
    defaults: {
        theme: 'auto',
        fontSize: 14,
        autoSave: true,
        notifications: true
    },
    
    // 获取配置
    get(key, defaultValue = null) {
        try {
            const config = JSON.parse(GM_getValue('config', '{}'));
            return config[key] !== undefined ? config[key] : 
                   (this.defaults[key] !== undefined ? this.defaults[key] : defaultValue);
        } catch (error) {
            utils.error(`读取配置失败: ${error.message}`);
            return this.defaults[key] || defaultValue;
        }
    },
    
    // 设置配置
    set(key, value) {
        try {
            const config = JSON.parse(GM_getValue('config', '{}'));
            config[key] = value;
            GM_setValue('config', JSON.stringify(config));
            return true;
        } catch (error) {
            utils.error(`保存配置失败: ${error.message}`);
            return false;
        }
    },
    
    // 重置配置
    reset() {
        GM_setValue('config', JSON.stringify(this.defaults));
    }
};
```

#### 数据迁移策略
```javascript
const DataMigration = {
    currentVersion: 3,
    
    migrate() {
        const dataVersion = GM_getValue('dataVersion', 1);
        
        if (dataVersion < this.currentVersion) {
            utils.log(`数据迁移: v${dataVersion} → v${this.currentVersion}`);
            
            for (let v = dataVersion; v < this.currentVersion; v++) {
                this[`migrateToV${v + 1}`]();
            }
            
            GM_setValue('dataVersion', this.currentVersion);
        }
    },
    
    migrateToV2() {
        // v1 → v2 迁移逻辑
        const oldConfig = GM_getValue('settings', null);
        if (oldConfig) {
            GM_setValue('config', oldConfig);
            GM_deleteValue('settings');
        }
    },
    
    migrateToV3() {
        // v2 → v3 迁移逻辑
        const config = JSON.parse(GM_getValue('config', '{}'));
        if (config.autoSign !== undefined) {
            config.autoSignIn = config.autoSign;
            delete config.autoSign;
            GM_setValue('config', JSON.stringify(config));
        }
    }
};
```

### 6. 样式管理最佳实践

#### CSS组织结构
```javascript
const StyleManager = {
    // 样式模块化
    styles: {
        base: `
            .script-container {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                line-height: 1.5;
            }
        `,
        
        buttons: `
            .script-button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 6px;
                color: white;
                cursor: pointer;
                padding: 8px 16px;
                transition: all 0.2s ease;
            }
            
            .script-button:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }
        `,
        
        responsive: `
            @media (max-width: 768px) {
                .script-container {
                    font-size: 12px;
                }
                
                .script-button {
                    padding: 6px 12px;
                }
            }
        `
    },
    
    // 注入样式
    inject() {
        const styleId = 'enhanced-script-styles';
        
        if (document.getElementById(styleId)) {
            return;
        }
        
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = Object.values(this.styles).join('\n');
        
        document.head.appendChild(style);
    },
    
    // 主题切换
    setTheme(theme) {
        const root = document.documentElement;
        
        switch (theme) {
            case 'dark':
                root.style.setProperty('--bg-color', '#1a1a1a');
                root.style.setProperty('--text-color', '#ffffff');
                break;
            case 'light':
                root.style.setProperty('--bg-color', '#ffffff');
                root.style.setProperty('--text-color', '#000000');
                break;
            default:
                // 自动主题
                const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                this.setTheme(isDark ? 'dark' : 'light');
        }
    }
};
```

### 7. 兼容性最佳实践

#### 功能检测模式
```javascript
const CompatibilityChecker = {
    // 检查基础支持
    checkBasicSupport() {
        return {
            gmApi: typeof GM_getValue === 'function',
            es6: typeof Promise !== 'undefined',
            dom: typeof document.querySelector === 'function',
            storage: typeof localStorage !== 'undefined'
        };
    },
    
    // 检查高级功能
    checkAdvancedSupport() {
        return {
            mutationObserver: typeof MutationObserver !== 'undefined',
            intersectionObserver: typeof IntersectionObserver !== 'undefined',
            fetch: typeof fetch !== 'undefined',
            asyncAwait: (async () => {})().constructor === Promise
        };
    },
    
    // 渐进式功能启用
    enableFeatures() {
        const basic = this.checkBasicSupport();
        const advanced = this.checkAdvancedSupport();
        
        if (!basic.gmApi || !basic.dom) {
            utils.error('基础功能不支持，脚本无法运行');
            return false;
        }
        
        // 启用基础功能
        enableBasicFeatures();
        
        // 条件性启用高级功能
        if (advanced.mutationObserver) {
            enableDynamicContentHandling();
        }
        
        if (advanced.fetch) {
            enableAdvancedNetworking();
        }
        
        return true;
    }
};
```

### 8. 调试和日志最佳实践

#### 日志系统
```javascript
const Logger = {
    levels: {
        ERROR: 0,
        WARN: 1,
        INFO: 2,
        DEBUG: 3
    },
    
    currentLevel: 2, // INFO级别
    
    log(level, message, ...args) {
        if (level <= this.currentLevel) {
            const timestamp = new Date().toISOString();
            const levelName = Object.keys(this.levels)[level];
            console.log(`[${timestamp}] [${SCRIPT_NAME}] [${levelName}] ${message}`, ...args);
        }
    },
    
    error: (msg, ...args) => Logger.log(Logger.levels.ERROR, msg, ...args),
    warn: (msg, ...args) => Logger.log(Logger.levels.WARN, msg, ...args),
    info: (msg, ...args) => Logger.log(Logger.levels.INFO, msg, ...args),
    debug: (msg, ...args) => Logger.log(Logger.levels.DEBUG, msg, ...args)
};
```

### 9. 用户体验最佳实践

#### 加载状态管理
```javascript
const LoadingManager = {
    show(message = '加载中...') {
        const loader = document.createElement('div');
        loader.id = 'script-loader';
        loader.innerHTML = `
            <div style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 10000;
            ">
                <div style="text-align: center;">
                    <div class="spinner"></div>
                    <div style="margin-top: 10px;">${message}</div>
                </div>
            </div>
        `;
        document.body.appendChild(loader);
    },
    
    hide() {
        const loader = document.getElementById('script-loader');
        if (loader) {
            loader.remove();
        }
    }
};

// 使用示例
async function performLongOperation() {
    LoadingManager.show('正在处理数据...');
    
    try {
        await longRunningTask();
        showSuccessMessage('操作完成');
    } catch (error) {
        showErrorMessage('操作失败');
    } finally {
        LoadingManager.hide();
    }
}
```

### 10. 安全最佳实践

#### 输入清理和验证
```javascript
const SecurityUtils = {
    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    
    // URL验证
    isValidUrl(url) {
        try {
            const urlObj = new URL(url);
            return ['http:', 'https:'].includes(urlObj.protocol);
        } catch {
            return false;
        }
    },
    
    // 清理用户输入
    sanitizeInput(input) {
        if (typeof input !== 'string') {
            return '';
        }
        
        return input
            .trim()
            .replace(/[<>]/g, '') // 移除潜在的HTML标签
            .substring(0, 1000); // 限制长度
    }
};
```

---

*最后更新: 2024年12月*
*实践版本: 1.0*
