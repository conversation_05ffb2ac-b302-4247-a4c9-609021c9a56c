# 成功实现模式

## 用户脚本开发模式

### 1. 标准UserScript模板模式

#### 模式描述
所有用户脚本都遵循标准的UserScript元数据格式和执行模式。

#### 实现模式
```javascript
// ==UserScript==
// @name         脚本名称
// @description  功能描述
// <AUTHOR>
// @version      版本号
// @match        *://目标网站/*
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function () {
    "use strict";
    
    // 防止在iframe中执行
    if (window.self !== window.top) {
        return;
    }
    
    // 主要功能实现
    function main() {
        // 功能代码
    }
    
    // 初始化
    main();
})();
```

#### 成功要素
- 完整的元数据声明
- 严格模式执行
- iframe检查防护
- 立即执行函数包装

### 2. 配置驱动模式

#### 模式描述
通过用户配置控制脚本功能的开启和行为定制。

#### 实现模式
```javascript
// 默认配置
const defaultSettings = {
    autoSign: true,
    blockAds: true,
    fontSize: 14,
    theme: 'dark'
};

// 获取用户配置
function getSettings() {
    const saved = GM_getValue('userSettings', '{}');
    return Object.assign({}, defaultSettings, JSON.parse(saved));
}

// 保存配置
function saveSettings(settings) {
    GM_setValue('userSettings', JSON.stringify(settings));
}

// 应用配置
function applySettings(settings) {
    if (settings.autoSign) enableAutoSign();
    if (settings.blockAds) blockAdvertisements();
    applyFontSize(settings.fontSize);
    applyTheme(settings.theme);
}
```

#### 成功要素
- 默认配置兜底
- 配置持久化存储
- 配置变更即时生效
- 向后兼容性考虑

### 3. 事件委托模式

#### 模式描述
使用事件委托处理动态生成的DOM元素，提高性能和可靠性。

#### 实现模式
```javascript
// 事件委托处理点击
document.addEventListener('click', function(e) {
    // 处理按钮点击
    if (e.target.matches('.custom-button')) {
        handleButtonClick(e.target);
    }
    
    // 处理链接点击
    if (e.target.matches('.enhanced-link')) {
        handleLinkClick(e.target);
    }
});

// DOM变化监听
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === 1) { // Element node
                    enhanceNewElements(node);
                }
            });
        }
    });
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});
```

#### 成功要素
- 单一事件监听器
- 动态内容自动处理
- 性能优化
- 内存泄漏防护

### 4. 渐进式增强模式

#### 模式描述
逐步添加功能，确保基础功能可用，高级功能可选。

#### 实现模式
```javascript
// 基础功能检查
function checkBasicSupport() {
    return typeof GM_getValue === 'function' && 
           typeof document.querySelector === 'function';
}

// 高级功能检查
function checkAdvancedSupport() {
    return typeof MutationObserver !== 'undefined' &&
           typeof fetch !== 'undefined';
}

// 分层初始化
function initialize() {
    if (!checkBasicSupport()) {
        console.warn('基础功能不支持');
        return;
    }
    
    // 启用基础功能
    enableBasicFeatures();
    
    if (checkAdvancedSupport()) {
        // 启用高级功能
        enableAdvancedFeatures();
    }
}
```

#### 成功要素
- 功能分层设计
- 兼容性检查
- 优雅降级
- 错误容错

### 5. 异步处理模式

#### 模式描述
使用Promise和async/await处理异步操作，提高代码可读性。

#### 实现模式
```javascript
// 异步数据获取
async function fetchUserData(userId) {
    try {
        const response = await new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'GET',
                url: `${baseURL}/api/user/${userId}`,
                onload: resolve,
                onerror: reject
            });
        });
        
        return JSON.parse(response.responseText);
    } catch (error) {
        console.error('获取用户数据失败:', error);
        return null;
    }
}

// 批量处理
async function processItems(items) {
    const results = [];
    
    for (const item of items) {
        try {
            const result = await processItem(item);
            results.push(result);
        } catch (error) {
            console.error(`处理项目失败: ${item.id}`, error);
        }
    }
    
    return results;
}
```

#### 成功要素
- Promise封装GM_API
- 错误处理机制
- 批量操作控制
- 性能优化考虑

### 6. 样式注入模式

#### 模式描述
动态注入CSS样式，实现界面美化和布局调整。

#### 实现模式
```javascript
// 样式注入函数
function addStyles() {
    const styleId = 'custom-enhancement-styles';
    
    // 避免重复注入
    if (document.getElementById(styleId)) {
        return;
    }
    
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
        .enhanced-element {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
        }
        
        .hidden-ads {
            display: none !important;
        }
        
        @media (max-width: 768px) {
            .enhanced-element {
                padding: 5px;
                margin: 3px 0;
            }
        }
    `;
    
    document.head.appendChild(style);
}
```

#### 成功要素
- 重复注入检查
- 响应式设计
- 样式隔离
- 性能优化

### 7. 工具函数模式

#### 模式描述
提取通用功能为工具函数，提高代码复用性。

#### 实现模式
```javascript
// 通用工具函数
const utils = {
    // 等待元素出现
    waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }
            
            const observer = new MutationObserver(() => {
                const element = document.querySelector(selector);
                if (element) {
                    observer.disconnect();
                    resolve(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素 ${selector} 未找到`));
            }, timeout);
        });
    },
    
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 创建按钮
    createButton(text, onClick, className = '') {
        const button = document.createElement('button');
        button.textContent = text;
        button.className = `custom-button ${className}`;
        button.addEventListener('click', onClick);
        return button;
    }
};
```

#### 成功要素
- 函数式设计
- 错误处理
- 参数验证
- 性能优化

## 架构模式

### 8. 模块化组织模式

#### 模式描述
将功能按模块组织，提高代码可维护性。基于实际项目分析，发现了区域化功能组织的成功模式。

#### 实际实现模式 (基于逛色花.js分析)
```javascript
// #region 全局变量
let activeTooltips = 0;
const baseURL = window.location.origin;

// 配置获取函数
function getSettings() {
    return {
        logoText: GM_getValue("logoText", ""),
        imageSize: GM_getValue("imageSize", "50px"),
        blockMedals: GM_getValue("blockMedals", 1),
        excludeGroup: getJSONValue("excludeGroup", "[]"),
        autoPagination: GM_getValue("autoPagination", true),
        // ... 更多配置项
    };
}
// #endregion

// #region 样式管理
function addStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .enhanced-element {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
        }
    `;
    document.head.appendChild(style);
}
// #endregion

// #region 用户签到功能
async function handleUserSign(buttonContainer) {
    const userid = getUserId();
    if (!userid) return;

    const lastSignDate = GM_getValue(`lastSignDate_${userid}`, null);
    const today = new Date().toLocaleDateString();
    const hasSignedToday = lastSignDate === today;

    const signButtonText = hasSignedToday ? "已经签到" : "快去签到";
    const signButton = createButton("signButton", signButtonText,
        () => window.location.href = `${baseURL}/plugin.php?id=dd_sign:index`);

    buttonContainer.appendChild(signButton);
}
// #endregion

// 主程序入口
function main() {
    const settings = getSettings();
    baseFunction(settings);
}
```

#### 成功要素
- **区域化组织**: 使用 `#region` 注释分组相关功能
- **配置驱动**: 统一的配置获取和管理机制
- **功能独立**: 每个区域可以独立开发和测试
- **错误隔离**: 区域间错误不会相互影响

### 9. 动态内容处理模式

#### 模式描述
处理现代网站的动态内容加载，确保脚本功能在AJAX更新后仍然有效。

#### 实际实现模式 (基于逛色花.js分析)
```javascript
// 观察器模式处理动态内容
function observeRedesignedLayout(settings) {
    const observer = new MutationObserver((mutations) => {
        let shouldUpdate = false;

        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // 检查是否有新的线程卡片
                const hasNewCards = Array.from(mutation.addedNodes).some(node =>
                    node.nodeType === 1 &&
                    (node.classList?.contains('thread-card') ||
                     node.querySelector?.('.thread-card'))
                );

                if (hasNewCards) {
                    shouldUpdate = true;
                }
            }
        });

        if (shouldUpdate) {
            // 延迟处理，避免频繁执行
            setTimeout(() => {
                blockContentByTitleInRedesignedLayout(settings);
                blockContentByUsersInRedesignedLayout(settings);
            }, 100);
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// 定期检查机制
function setupPeriodicCheck(settings) {
    // 定期检查功能，确保不遗漏任何内容
    setInterval(() => {
        const uncheckedCards = document.querySelectorAll('.thread-card:not([data-keyword-checked])');
        if (uncheckedCards.length > 0) {
            blockContentByTitleInRedesignedLayout(settings);
        }
    }, 3000);

    // 强制重新检查所有卡片
    setInterval(() => {
        forceRecheckAllCards(settings);
    }, 10000);
}
```

#### 成功要素
- **MutationObserver**: 监听DOM变化
- **防抖处理**: 避免频繁执行
- **标记机制**: 防止重复处理
- **定期检查**: 确保不遗漏内容

### 10. 延迟初始化模式

#### 模式描述
根据页面加载状态和内容可用性，智能地延迟初始化功能模块。

#### 实际实现模式 (基于逛色花.js分析)
```javascript
function baseFunction(settings) {
    // 立即执行的基础功能
    removeAD();
    addStyles();
    manipulateMedals(settings);

    // 延迟执行的复杂功能
    const executeBlockingWithRetry = (retryCount = 0, maxRetries = 10) => {
        const isForumDisplayPage = /forum\.php\?mod=forumdisplay|\/forum-\d+-\d+\.html/.test(window.location.href);

        if (isForumDisplayPage) {
            const redesignedContainer = document.querySelector('.redesigned-thread-list');
            const threadCards = document.querySelectorAll('.thread-card');

            // 检查内容是否已加载
            if ((redesignedContainer && threadCards.length > 0) || retryCount >= maxRetries) {
                blockContentByUsers(settings);
                blockContentByTitle(settings);
                observeRedesignedLayout(settings);
            } else {
                // 递归重试
                setTimeout(() => executeBlockingWithRetry(retryCount + 1, maxRetries), 500);
            }
        } else {
            // 非论坛页面直接执行
            blockContentByUsers(settings);
            blockContentByTitle(settings);
        }
    };

    // 延迟700ms后开始执行
    setTimeout(() => executeBlockingWithRetry(), 700);
}
```

#### 成功要素
- **条件检查**: 检查必要元素是否存在
- **重试机制**: 自动重试直到成功或达到上限
- **页面类型判断**: 根据页面类型采用不同策略
- **优雅降级**: 重试失败时仍能提供基础功能

---

*最后更新: 2024年12月*
*模式版本: 1.0*
