# JavaScript/UserScript 技术栈

## 核心技术

### 运行时环境
- **浏览器**: Firefox, Chrome, Edge, Safari
- **脚本管理器**: Tampermonkey v5.4.6225
- **JavaScript版本**: ES2015+ (ES6及以上)
- **执行环境**: 浏览器页面上下文

### 主要技术栈
- **语言**: JavaScript (纯JS，无TypeScript)
- **API**: Tampermonkey GM_API + Web APIs
- **样式**: CSS3 + 动态样式注入
- **存储**: GM_getValue/GM_setValue (Tampermonkey存储API)

## 开发工具和环境

### 脚本管理器功能
- **脚本注入**: 自动注入到匹配的网页
- **API权限**: GM_getValue, GM_setValue, GM_xmlhttpRequest等
- **跨域支持**: 绕过同源策略限制
- **用户界面**: 脚本管理和配置界面

### 开发工具
- **编辑器**: Tampermonkey内置编辑器 + 外部IDE
- **调试工具**: 浏览器开发者工具
- **版本控制**: 文件系统 + 手动版本管理
- **测试环境**: 直接在目标网站测试

### 构建和部署
- **构建工具**: 无需构建，直接部署
- **包管理器**: 无需包管理器
- **部署方式**: 
  - 直接安装到Tampermonkey
  - 文件分享和手动安装
  - 用户脚本网站分发

## 项目结构

### 文件组织
```
项目根目录/
├── <EMAIL>/     # Tampermonkey扩展源码
│   ├── manifest.json                # 扩展清单
│   ├── background.js                # 后台脚本
│   ├── content.js                   # 内容脚本
│   ├── extension.js                 # 扩展核心逻辑
│   └── ...                         # 其他扩展文件
├── 逛色花.js                        # 色花堂增强脚本
├── nodeseek论坛美化.js              # NodeSeek美化脚本
├── linux.do美化.js                 # Linux.do美化脚本
├── tampermonkey-card-layout.css     # Tampermonkey界面样式
├── tampermonkey-button-organizer.js # 按钮组织工具
└── ...                             # 其他用户脚本
```

### 脚本分类
1. **论坛美化脚本**: 界面优化和视觉增强
2. **功能增强脚本**: 添加新功能和便利工具
3. **内容管理脚本**: 内容过滤和屏蔽
4. **工具脚本**: 开发和管理工具

## 技术特性

### Tampermonkey GM_API
```javascript
// 数据存储
GM_getValue(key, defaultValue)
GM_setValue(key, value)
GM_deleteValue(key)

// 网络请求
GM_xmlhttpRequest({
    method: 'GET',
    url: 'https://api.example.com/data',
    onload: function(response) {
        // 处理响应
    }
})

// 菜单注册
GM_registerMenuCommand('设置', showSettings)

// 资源获取
GM_getResourceText('css')
GM_getResourceURL('image')
```

### Web APIs使用
```javascript
// DOM操作
document.querySelector()
document.addEventListener()
MutationObserver()

// 现代JavaScript特性
async/await
Promise
fetch() // 在支持的环境中
localStorage // 作为备选存储

// CSS操作
document.createElement('style')
element.style.setProperty()
getComputedStyle()
```

### 跨域能力
- **GM_xmlhttpRequest**: 绕过CORS限制
- **跨域数据获取**: 访问任意API
- **资源加载**: 加载外部CSS/JS资源
- **iframe通信**: 跨域iframe数据交换

## 性能优化

### 脚本加载优化
- **@run-at document-idle**: 页面加载完成后执行
- **条件执行**: 检查页面类型后执行相应功能
- **延迟初始化**: 非关键功能延迟加载
- **资源缓存**: 缓存频繁访问的DOM元素

### 内存管理
- **事件清理**: 页面卸载时清理事件监听器
- **定时器管理**: 及时清理setTimeout/setInterval
- **DOM引用**: 避免长期持有DOM元素引用
- **闭包优化**: 避免不必要的闭包内存占用

### 执行效率
- **事件委托**: 减少事件监听器数量
- **批量DOM操作**: 使用DocumentFragment
- **防抖节流**: 控制高频事件处理
- **选择器优化**: 使用高效的CSS选择器

## 兼容性策略

### 浏览器兼容性
- **现代浏览器**: Chrome 80+, Firefox 75+, Safari 13+
- **API检测**: 功能检测而非浏览器检测
- **渐进增强**: 基础功能+可选高级功能
- **优雅降级**: 不支持的功能自动禁用

### Tampermonkey版本兼容
- **最低版本**: Tampermonkey 4.0+
- **API兼容**: 使用稳定的GM_API
- **功能检测**: 检查API可用性
- **向后兼容**: 保持旧版本脚本可用

### 网站兼容性
- **DOM结构变化**: 使用灵活的选择器
- **AJAX内容**: MutationObserver监听变化
- **SPA应用**: 监听路由变化
- **移动端适配**: 响应式设计

## 开发规范

### 代码规范
- **严格模式**: 使用 "use strict"
- **命名约定**: 驼峰命名法
- **注释规范**: JSDoc风格注释
- **错误处理**: 完整的try-catch机制

### 脚本元数据规范
```javascript
// ==UserScript==
// @name         脚本名称
// @description  功能描述
// <AUTHOR>
// @version      1.0.0
// @match        *://目标网站/*
// @grant        GM_getValue
// @grant        GM_setValue
// @run-at       document-idle
// ==/UserScript==
```

### 安全规范
- **输入验证**: 验证所有用户输入
- **XSS防护**: HTML内容转义
- **CSRF防护**: 验证请求来源
- **权限最小化**: 只申请必要的权限

## 常用命令和工具

### 开发命令
```bash
# 无需构建命令，直接编辑JS文件

# 安装脚本到Tampermonkey
# 1. 复制脚本内容
# 2. 在Tampermonkey中创建新脚本
# 3. 粘贴内容并保存

# 调试脚本
# 1. 在浏览器中打开目标网站
# 2. 打开开发者工具
# 3. 在Console中查看脚本输出
```

### 测试流程
1. **本地测试**: 在Tampermonkey中直接测试
2. **多浏览器测试**: 在不同浏览器中验证
3. **功能测试**: 验证所有功能正常工作
4. **兼容性测试**: 测试不同网站版本的兼容性

### 部署流程
1. **脚本完成**: 完成开发和测试
2. **版本标记**: 更新版本号和更新日志
3. **文档更新**: 更新使用说明和功能介绍
4. **分发**: 通过文件分享或脚本网站分发

## 项目依赖

### 核心依赖
- **Tampermonkey**: 脚本运行环境
- **现代浏览器**: 支持ES6+的浏览器
- **目标网站**: 脚本运行的目标网站

### 可选依赖
- **外部CSS库**: 用于样式增强
- **图标字体**: Font Awesome等图标库
- **第三方API**: 数据获取和功能扩展

### 运行时要求
- **JavaScript启用**: 浏览器必须启用JavaScript
- **Tampermonkey权限**: 脚本需要相应的执行权限
- **网络连接**: 部分功能需要网络访问

---

*最后更新: 2024年12月*
*技术栈版本: 1.0*
