# 项目架构设计

## 项目概述

这是一个JavaScript用户脚本开发项目，包含Tampermonkey浏览器扩展和多个网站增强脚本。项目专注于网站美化、功能增强和用户体验优化。

## 技术架构

### 核心组件

#### 1. Tampermonkey扩展 (`<EMAIL>/`)
- **类型**: Firefox浏览器扩展
- **版本**: 5.4.6225
- **功能**: 用户脚本管理器，提供脚本注入、存储、API等功能
- **关键文件**:
  - `manifest.json` - 扩展清单文件
  - `background.js` - 后台脚本
  - `content.js` - 内容脚本
  - `extension.js` - 扩展核心逻辑

#### 2. 用户脚本集合
- **论坛美化脚本**: 
  - `nodeseek论坛美化.js` - NodeSeek论坛界面优化
  - `linux.do美化.js` - Linux.do社区美化
  - `远景论坛美化.js` - 远景论坛界面增强
  - `贴吧美化.js` - 百度贴吧界面优化
- **功能增强脚本**:
  - `逛色花.js` - 色花堂论坛功能增强（签到、搜索、预览等）
  - `JAVDB-增强.js` - JAVDB网站功能扩展
  - `V2ex.js` - V2EX社区增强
- **内容管理脚本**:
  - `Linux DO 屏蔽.js` - 内容过滤脚本
  - `贴吧内容屏蔽.js` - 贴吧内容管理
- **工具脚本**:
  - `tampermonkey-card-layout.css` - Tampermonkey界面卡片布局
  - `tampermonkey-button-organizer.js` - 按钮组织工具

### 架构模式

#### 1. 模块化设计
- 每个脚本独立开发，专注特定网站或功能
- 通用功能抽取为可复用模块
- 配置与逻辑分离

#### 2. 事件驱动架构
- 基于DOM事件和页面生命周期
- 异步处理和回调机制
- 用户交互响应式设计

#### 3. 插件化扩展
- Tampermonkey作为运行时环境
- 脚本通过GM_API与浏览器交互
- 支持跨域请求和本地存储

## 数据流架构

### 脚本执行流程
1. **注入阶段**: Tampermonkey将脚本注入目标页面
2. **初始化阶段**: 脚本检测页面环境和用户配置
3. **功能激活**: 根据页面类型激活相应功能模块
4. **事件监听**: 监听用户操作和页面变化
5. **数据持久化**: 通过GM_setValue保存用户设置

### 存储架构
- **本地存储**: 使用GM_getValue/GM_setValue存储用户配置
- **会话存储**: 临时数据和状态管理
- **跨域通信**: 通过GM_xmlhttpRequest实现跨域数据获取

## 开发模式

### 文件组织
- 根目录存放独立的用户脚本文件
- `<EMAIL>/` 目录包含扩展源码
- CSS文件提供样式定制
- 版本管理通过文件名区分（如 `old.js`）

### 代码规范
- 使用严格模式 (`"use strict"`)
- 遵循UserScript元数据规范
- 统一的错误处理和日志记录
- 响应式设计适配不同屏幕尺寸

## 部署架构

### 开发环境
- 本地文件系统开发
- Tampermonkey扩展作为测试环境
- 浏览器开发者工具调试

### 分发机制
- 直接文件分享
- 用户手动安装到Tampermonkey
- 支持自动更新检查

## 扩展性设计

### 新脚本集成
- 遵循现有命名约定
- 实现标准的初始化和清理流程
- 使用通用的工具函数和样式

### 功能模块化
- 可插拔的功能组件
- 配置驱动的功能开关
- 向后兼容的API设计

## 性能考虑

### 优化策略
- 延迟加载非关键功能
- DOM操作批量处理
- 事件委托减少监听器数量
- 缓存频繁访问的DOM元素

### 资源管理
- 避免内存泄漏
- 及时清理事件监听器
- 控制定时器和异步操作

## 安全架构

### 权限控制
- 最小权限原则
- 跨域请求白名单
- 用户数据加密存储

### 代码安全
- 输入验证和清理
- XSS防护措施
- CSP策略遵循

---

*最后更新: 2024年12月*
*架构版本: 1.0*
