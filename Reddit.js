// ==UserScript==
// @name         Reddit
// @description  给所有Reddit帖子添加边框、阴影、圆角和调整间距 (纯CSS)
// <AUTHOR>
// @version      0.3
// @match        http*://www.reddit.com/*
// @grant        GM_addStyle
// ==/UserScript==

(function () {
	"use strict";

	// 定义CSS样式规则
	const css = `
div[data-testid="post-container"],
div[data-testid="platform-xi-post-container"],
div.Post,
shreddit-post {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
    padding-bottom: 1.0rem !important;
    box-shadow: 0 1px 16px rgba(0, 0, 0, 0.15) !important;
    outline: 1px solid rgba(0, 0, 0, 0.10) !important;
    border-radius: 12px !important;
    margin-bottom: 15px !important;
    margin-top: 10px !important;
    overflow: hidden !important;
}
.border-solid {
    border-style: none !important;
}
.button.icon {
display: none  !important;
}
    `;

	// 使用 GM_addStyle 来注入 CSS (Tampermonkey 推荐的方式)
	// 如果 GM_addStyle 不可用（例如在非 Tampermonkey 环境），则回退到创建 <style> 标签
	if (typeof GM_addStyle !== "undefined") {
		GM_addStyle(css);
	} else {
		const style = document.createElement("style");
		style.textContent = css;
		document.head.appendChild(style);
		console.log("Reddit帖子美化脚本：使用 <style> 标签注入 CSS");
	}

	console.log("Reddit帖子美化脚本已加载 (优化版 v0.3)");
})();
