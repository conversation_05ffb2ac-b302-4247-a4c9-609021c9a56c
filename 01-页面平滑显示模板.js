// ==UserScript==
// @name         页面平滑显示模板
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  页面平滑显示模板，防止页面加载过程中出现闪烁
// <AUTHOR>
// @match        https://example.com/*
// @grant        GM_addStyle
// @run-at       document-start
// @license      MIT
// ==/UserScript==

/**
 * 简化版页面平滑显示模板
 * 
 * 功能：在页面加载开始时隐藏内容，完成后平滑显示，防止闪烁
 * 使用：修改 @match 为你需要匹配的网站，在 addCustomStyles 添加自定义样式
 */

// 配置项
const TRANSITION_DURATION = 300;  // 过渡动画持续时间（毫秒）
const TRANSITION_DELAY = 100;     // 页面显示延迟时间（毫秒）

// 初始化页面隐藏样式（防止闪烁）
(function() {
    const initialHideStyle = document.createElement("style");
    initialHideStyle.textContent = ` 
    body { 
        opacity: 0 !important; 
        visibility: hidden !important; 
    } 
    body.script-ready { 
        opacity: 1 !important; 
        visibility: visible !important;
        transition: opacity ${TRANSITION_DURATION}ms ease;
    } 
    `;
    document.head.appendChild(initialHideStyle);
})();

(function() {
    'use strict';
    
    // 添加自定义样式
    function addCustomStyles() {
        const styles = `
            /* 在这里添加你的自定义样式 */
            body {
                /* 示例样式 */
                /* background-color: #f5f5f5; */
            }
        `;
        
        if (typeof GM_addStyle === 'function') {
            GM_addStyle(styles);
        } else {
            const styleElement = document.createElement('style');
            styleElement.textContent = styles;
            document.head.appendChild(styleElement);
        }
    }
    
    // 初始化函数
    function init() {
        try {
            // 添加你的自定义样式
            addCustomStyles();
            
            // 在这里添加其他初始化逻辑
            // ...
            
            // 页面准备完成，添加class使页面显示
            setTimeout(() => {
                document.body.classList.add('script-ready');
            }, TRANSITION_DELAY);
        } catch (error) {
            // 确保页面在出错时也能显示
            document.body.classList.add('script-ready');
        }
    }
    
    // 运行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})(); 